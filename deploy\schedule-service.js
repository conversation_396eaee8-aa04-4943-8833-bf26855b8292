const { google } = require('googleapis');

// Сервис для получения и кеширования расписания рейдов
class ScheduleService {
    constructor(serviceAccount) {
        this.scheduleCache = null;
        this.scheduleCacheTime = null;
        this.scheduleCacheDuration = 10 * 60 * 1000; // 10 минут в миллисекундах
        this.isRunning = false;
        this.intervalId = null;
        this.updateInterval = 10 * 60 * 1000; // 10 минут

        // Конфигурация для получения расписания рейдов
        this.SCHEDULE_SHEETS_CONFIG = {
            SPREADSHEET_ID: '12Z36TfBEWKDLEGBEtSbTjMqAlRe6A3drnNcfQIc_pNk'
        };

        // Инициализация Google Sheets API с Service Account
        if (serviceAccount) {
            this.auth = new google.auth.GoogleAuth({
                credentials: serviceAccount,
                scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly']
            });
            this.sheets = google.sheets({ version: 'v4', auth: this.auth });
        } else {
            console.error('[ScheduleService] Service Account не предоставлен');
        }

        // Конфигурация дней недели (индексы в массиве данных)
        this.daysConfig = [
            { name: 'Wednesday', dateIdx: 0, dayIdx: 1, dataIdx: 2 },
            { name: 'Thursday', dateIdx: 3, dayIdx: 4, dataIdx: 5 },
            { name: 'Friday', dateIdx: 6, dayIdx: 7, dataIdx: 8 },
            { name: 'Saturday', dateIdx: 9, dayIdx: 10, dataIdx: 11 },
            { name: 'Sunday', dateIdx: 12, dayIdx: 13, dataIdx: 14 },
            { name: 'Monday', dateIdx: 15, dayIdx: 16, dataIdx: 17 },
            { name: 'Tuesday', dateIdx: 18, dayIdx: 19, dataIdx: 20 }
        ];
    }

    // Запуск автоматического обновления расписания
    start() {
        if (this.isRunning) {
            console.log('[ScheduleService] Сервис уже запущен');
            return;
        }

        console.log('[ScheduleService] Запускаем сервис автообновления расписания каждые 10 минут...');
        this.isRunning = true;

        // Первоначальная загрузка
        this.updateSchedule().then(() => {
            console.log('[ScheduleService] Первоначальная загрузка расписания завершена');
        }).catch(error => {
            console.error('[ScheduleService] Ошибка первоначальной загрузки расписания:', error.message);
        });

        // Периодическое обновление каждые 10 минут
        this.intervalId = setInterval(() => {
            this.updateSchedule().catch(error => {
                console.error('[ScheduleService] Ошибка периодического обновления расписания:', error.message);
            });
        }, this.updateInterval);
    }

    // Остановка сервиса
    stop() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
        this.isRunning = false;
        console.log('[ScheduleService] Сервис остановлен');
    }

    // Получение всех данных расписания одним запросом через Google Sheets API
    async fetchAllScheduleData() {
        if (!this.sheets) {
            throw new Error('Google Sheets API не инициализирован');
        }

        const config = this.SCHEDULE_SHEETS_CONFIG;
        const ranges = [
            'B63:F63', 'B64:F64', 'B65:C91',    // Среда
            'I63:M63', 'I64:M64', 'I65:J91',    // Четверг
            'P63:T63', 'P64:T64', 'P65:Q91',    // Пятница
            'W63:AA63', 'W64:AA64', 'W65:X91',  // Суббота
            'B116:F116', 'B117:F117', 'B118:C142', // Воскресенье
            'I116:M116', 'I117:M117', 'I118:J142', // Понедельник
            'P116:T116', 'P117:T117', 'P118:Q142'  // Вторник
        ];

        try {
            const response = await this.sheets.spreadsheets.get({
                spreadsheetId: config.SPREADSHEET_ID,
                ranges: ranges,
                includeGridData: true
            });

            if (!response.data.sheets || !response.data.sheets[0] || !response.data.sheets[0].data) {
                throw new Error('Неверный формат ответа от Google Sheets API');
            }

            return response.data.sheets[0].data;

        } catch (error) {
            console.error('[ScheduleService] Ошибка получения данных из Google Sheets:', error.message);
            throw error;
        }
    }

    // Утилиты для обработки данных расписания
    parseTime(timeValue) {
        return timeValue?.toString().replace(/\s*(CEST|CET|UTC|GMT).*$/i, '').trim() || '';
    }

    parseDayName(dayString) {
        if (!dayString) return '';

        // Если есть разделитель "/", берем английскую часть (после "/")
        if (dayString.includes('/')) {
            return dayString.split('/')[1].trim();
        }

        // Если нет разделителя, возвращаем как есть
        return dayString.trim();
    }

    isStrikethrough(cellData) {
        return cellData?.effectiveFormat?.textFormat?.strikethrough === true;
    }

    // Обработка одного дня
    processDaySchedule(dayConfig, allData) {
        const dateData = allData[dayConfig.dateIdx];
        const dayNameData = allData[dayConfig.dayIdx];
        const scheduleData = allData[dayConfig.dataIdx];

        const dateString = dateData.rowData?.[0]?.values?.[0]?.formattedValue || '';
        const dayName = this.parseDayName(dayNameData.rowData?.[0]?.values?.[0]?.formattedValue) || dayConfig.name;
        const daySchedule = [];

        if (!scheduleData.rowData) return daySchedule;

        for (let i = 0; i < scheduleData.rowData.length; i++) {
            const row = scheduleData.rowData[i];
            if (!row.values || row.values.length < 2) continue;

            const timeValue = row.values[0]?.formattedValue;
            const taskValue = row.values[1]?.formattedValue;

            if (!timeValue || !taskValue) continue;

            // Проверяем зачеркивание
            if (this.isStrikethrough(row.values[0]) || this.isStrikethrough(row.values[1])) continue;

            daySchedule.push({
                day: dayName,
                date: dateString,
                time: this.parseTime(timeValue),
                event: taskValue.toString().trim(),
                active: "" // Пустое значение означает доступно для записи
            });
        }

        return daySchedule;
    }

    // Обновление расписания
    async updateSchedule() {
        try {
            console.log('[ScheduleService] Обновляем расписание...');

            // Получаем все данные одним запросом
            const allData = await this.fetchAllScheduleData();
            const allScheduleData = [];

            // Обрабатываем все дни
            this.daysConfig.forEach(dayConfig => {
                const daySchedule = this.processDaySchedule(dayConfig, allData);
                console.log(`[ScheduleService] ${dayConfig.name}: ${daySchedule.length} записей`);
                allScheduleData.push(...daySchedule);
            });

            // Формируем ответ в том же формате, что и оригинальный API
            const result = {
                result: "success",
                message: "Данные расписания успешно получены",
                data: allScheduleData,
                total_records: allScheduleData.length,
                cached_at: new Date().toISOString()
            };

            console.log(`[ScheduleService] Получено ${allScheduleData.length} записей расписания`);

            // Сохраняем в кеш
            this.scheduleCache = result;
            this.scheduleCacheTime = Date.now();
            console.log('[ScheduleService] Расписание обновлено и сохранено в кеш');

            return result;

        } catch (error) {
            console.error('[ScheduleService] Ошибка обновления расписания:', error);
            throw error;
        }
    }

    // Получение кешированного расписания
    getSchedule(forceRefresh = false) {
        // Если запрошено принудительное обновление
        if (forceRefresh) {
            return this.updateSchedule();
        }

        // Проверяем кеш
        const now = Date.now();
        if (this.scheduleCache && this.scheduleCacheTime &&
            (now - this.scheduleCacheTime) < this.scheduleCacheDuration) {
            console.log('[ScheduleService] Возвращаем кешированное расписание');
            return Promise.resolve(this.scheduleCache);
        }

        // Если кеш устарел, обновляем
        console.log('[ScheduleService] Кеш устарел, обновляем расписание...');
        return this.updateSchedule();
    }

    // Фильтрация доступных рейдов по типу
    filterAvailableRaids(scheduleData, raidType) {
        try {
            console.log(`[ScheduleService] Фильтруем ${raidType} рейды из ${scheduleData.length} записей`);

            const filteredRaids = scheduleData.filter(entry => {
                // Проверяем, что есть событие
                if (!entry.event || typeof entry.event !== 'string') {
                    return false;
                }

                // Проверяем, что active поле пустое (доступно для записи)
                if (entry.active && entry.active.trim() !== '') {
                    return false;
                }

                const eventLower = entry.event.toLowerCase();

                // Фильтруем по типу рейда
                if (raidType === 'normal') {
                    // Ищем точно "LoU Normal"
                    return eventLower === 'lou normal';
                } else if (raidType === 'heroic') {
                    // Ищем точно "LoU Heroic"
                    return eventLower === 'lou heroic';
                } else if (raidType === 'mythic') {
                    // Ищем все что содержит "LoU Mythic"
                    return eventLower.includes('lou mythic');
                }

                return false;
            });

            console.log(`[ScheduleService] Найдено ${filteredRaids.length} доступных ${raidType} рейдов`);
            return filteredRaids;

        } catch (error) {
            console.error(`[ScheduleService] Ошибка фильтрации ${raidType} рейдов:`, error);
            return [];
        }
    }

    // Получение статистики сервиса
    getStats() {
        return {
            isRunning: this.isRunning,
            updateInterval: this.updateInterval,
            cacheAge: this.scheduleCacheTime ? Date.now() - this.scheduleCacheTime : null,
            cacheDuration: this.scheduleCacheDuration,
            totalRecords: this.scheduleCache ? this.scheduleCache.total_records : 0,
            lastUpdate: this.scheduleCacheTime ? new Date(this.scheduleCacheTime).toISOString() : null
        };
    }

    // Очистка кеша (для отладки)
    clearCache() {
        this.scheduleCache = null;
        this.scheduleCacheTime = null;
        console.log('[ScheduleService] Кеш расписания очищен');
    }
}

module.exports = ScheduleService;
