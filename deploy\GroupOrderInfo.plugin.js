/**
 * @name GroupOrderInfo
 * <AUTHOR>
 * @description Показывает информацию о заказах из Google Sheets под списком пользователей в групповых чатах
 * @version 1.0.1
 * @source https://github.com/yourusername/GroupOrderInfo
 * @updateUrl https://raw.githubusercontent.com/yourusername/GroupOrderInfo/main/GroupOrderInfo.plugin.js
 */

// Определяем класс в глобальном контексте для загрузки через MeldTeamPlugins
const GroupOrderInfo = class {
    constructor() {
        // Основные свойства плагина
        this.orderInfoContainer = null;
        this.currentChannelId = null;
        this.channelCheckInterval = null;
        this.lastProcessedOrder = null;
        this.isProcessing = false;
        this.discordSearchWs = null;
        this.discordSearchWsUrl = 'wss://discord-6r4h.onrender.com'; // Основной прокси-сервер
        this.activationCode = null;
        this.isActivated = false;
        this.tempStorage = {}; // Временное хранилище если localStorage недоступен
        this.reconnectInterval = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 10;
        this.fieldMetadata = null; // Кеш метаданных полей

        // Загружаем кешированные метаданные при инициализации
        const cachedMetadata = this.getStorageItem('fieldMetadata');
        const cacheTime = this.getStorageItem('fieldMetadata_time');
        const now = Date.now();

        if (cachedMetadata && cacheTime && (now - cacheTime < 10 * 60 * 1000)) {
            this.fieldMetadata = cachedMetadata;
            this.debug('Загружены кешированные метаданные полей из localStorage');
        }

        // Настройки логирования
        this.debugMode = false; // Включить для детального логирования
        this.logLevel = 'info'; // 'debug', 'info', 'warn', 'error'

        // Оптимизация обработки каналов
        this.updateTimeout = null; // Таймер для debouncing
        this.currentRequest = null; // Текущий запрос для отмены
        this.requestQueue = new Map(); // Очередь запросов по каналам
        this.processingChannel = null; // Канал который сейчас обрабатывается
        this.retryAttempts = new Map(); // Счетчик попыток для каналов

        // Инициализируем модули Discord
        this.initDiscordModules();
    }

    // --- Стандартные методы BetterDiscord ---
    getName() { return "GroupOrderInfo"; }
    getAuthor() { return "YourName"; }
    getDescription() { return "Показывает информацию о заказах из Google Sheets под списком пользователей в групповых чатах"; }
    getVersion() { return "1.0.1"; }

    // Инициализация модулей Discord
    initDiscordModules() {
        try {
            // Сначала пробуем использовать ZeresPluginLibrary, если доступна
            const ZLib = window.ZLibrary || global.ZeresPluginLibrary;
            if (ZLib) {
                this.log('Найдена ZeresPluginLibrary, используем её модули');
                try {
                    const Library = ZLib.Library || ZLib;
                    const DiscordModules = Library.DiscordModules;

                    if (DiscordModules) {
                        const { UserStore, ChannelStore, NavigationUtils, SelectedChannelStore } = DiscordModules;

                        this.UserStore = UserStore;
                        this.ChannelStore = ChannelStore;
                        this.NavigationUtils = NavigationUtils;
                        this.SelectedChannelStore = SelectedChannelStore;

                        this.log('Модули из ZeresPluginLibrary успешно загружены');
                        this.log('UserStore: ' + !!this.UserStore);
                        this.log('ChannelStore: ' + !!this.ChannelStore);
                        this.log('SelectedChannelStore: ' + !!this.SelectedChannelStore);
                        this.log('NavigationUtils: ' + !!this.NavigationUtils);

                        if (this.NavigationUtils) {
                            this.log('NavigationUtils методы: ' + Object.keys(this.NavigationUtils).join(', '));
                        }
                        return;
                    }
                } catch (error) {
                    this.log('Ошибка при использовании ZeresPluginLibrary: ' + error.message, 'warn');
                }
            }

            // Fallback: получаем модули через BdApi.Webpack
            this.log('Используем BdApi.Webpack для получения модулей');
            this.UserStore = BdApi.Webpack.getModule(BdApi.Webpack.Filters.byProps('getCurrentUser', 'getUser'));
            this.ChannelStore = BdApi.Webpack.getModule(BdApi.Webpack.Filters.byProps('getChannel', 'getDMFromUserId'));
            this.SelectedChannelStore = BdApi.Webpack.getModule(BdApi.Webpack.Filters.byProps('getChannelId', 'getLastSelectedChannelId'));

            // Пробуем разные способы получить NavigationUtils
            this.NavigationUtils = BdApi.Webpack.getModule(BdApi.Webpack.Filters.byProps('transitionTo', 'replaceWith')) ||
                                   BdApi.Webpack.getModule(BdApi.Webpack.Filters.byProps('transitionTo')) ||
                                   BdApi.Webpack.getModule(m => m.transitionTo && typeof m.transitionTo === 'function');

            this.log('Discord модули инициализированы через BdApi');
            this.log('UserStore: ' + !!this.UserStore);
            this.log('ChannelStore: ' + !!this.ChannelStore);
            this.log('SelectedChannelStore: ' + !!this.SelectedChannelStore);
            this.log('NavigationUtils: ' + !!this.NavigationUtils);

            if (this.NavigationUtils) {
                this.log('NavigationUtils методы: ' + Object.keys(this.NavigationUtils).join(', '));
            }
        } catch (error) {
            this.log('Ошибка инициализации Discord модулей: ' + error.message, 'error');
        }
    }

    // Оптимизированный логгер с уровнями
    log(message, type = 'log') {
        const logLevels = { debug: 0, log: 1, info: 1, warn: 2, error: 3 };
        const currentLevel = logLevels[this.logLevel] || 1;
        const messageLevel = logLevels[type] || 1;

        // Показываем только важные сообщения или в debug режиме
        if (this.debugMode || messageLevel >= 2) { // warn и error всегда, остальное только в debug
            const prefix = `[${this.getName()}]`;
            console[type](`${prefix} ${message}`);
        }
    }

    // Быстрые методы для разных уровней
    debug(message) { this.log(message, 'debug'); }
    info(message) { this.log(message, 'info'); }
    warn(message) { this.log(message, 'warn'); }
    error(message) { this.log(message, 'error'); }

    // Принудительное обновление данных (игнорируя кеш)
    async forceUpdateOrderInfo() {
        this.info('Принудительное обновление данных...');

        // Сбрасываем кеш
        this.lastProcessedOrder = null;

        // Отменяем текущие операции
        if (this.updateTimeout) {
            clearTimeout(this.updateTimeout);
            this.updateTimeout = null;
        }

        if (this.currentRequest) {
            this.currentRequest.abort();
            this.currentRequest = null;
        }

        this.processingChannel = null;

        // Показываем индикатор загрузки
        this.showLoadingIndicator();

        try {
            // Получаем текущий канал и номер заказа
            if (!this.ChannelStore || !this.currentChannelId) {
                this.warn('Нет активного канала для обновления');
                return;
            }

            const channel = this.ChannelStore.getChannel(this.currentChannelId);
            if (!channel) {
                this.warn('Канал не найден');
                return;
            }

            const orderNumber = this.extractOrderNumber(channel.name);
            if (!orderNumber) {
                this.warn('Номер заказа не найден в названии канала');
                return;
            }

            // Принудительно получаем данные
            const abortController = new AbortController();
            this.currentRequest = abortController;

            const orderData = await this.fetchSheetDataOptimized(orderNumber, abortController.signal, true);

            // Создаем и отображаем обновленный элемент
            const orderInfoElement = await this.createOrderInfoElement(orderData);
            this.removeOrderInfo();

            // Находим место для вставки
            let sidebar = document.querySelector('[class*="members_"]');
            if (!sidebar) {
                sidebar = document.querySelector('[class*="members-"]');
            }
            if (!sidebar) {
                sidebar = document.querySelector('div[class*="members"]');
            }

            if (sidebar) {
                this.orderInfoContainer = orderInfoElement;
                sidebar.appendChild(this.orderInfoContainer);
                this.debug('Принудительно обновленная информация добавлена в sidebar');
            }

            // Обновляем кеш
            const cacheKey = `${this.currentChannelId}_${orderNumber}`;
            this.lastProcessedOrder = cacheKey;
            this.currentRequest = null;

            this.info('Принудительное обновление завершено');
        } catch (error) {
            if (error.name !== 'AbortError') {
                this.error('Ошибка при принудительном обновлении: ' + error.message);
            }
        } finally {
            this.hideLoadingIndicator();
        }
    }

    // Показывает индикатор загрузки
    showLoadingIndicator() {
        if (this.orderInfoContainer) {
            const loadingDiv = document.createElement('div');
            loadingDiv.id = 'force-update-loading';
            loadingDiv.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.7);
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 8px;
                z-index: 1000;
            `;
            loadingDiv.innerHTML = `
                <div style="color: white; font-size: 12px; display: flex; align-items: center; gap: 8px;">
                    <div style="
                        width: 16px;
                        height: 16px;
                        border: 2px solid #ffffff40;
                        border-top: 2px solid #ffffff;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                    "></div>
                    Обновление...
                </div>
                <style>
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                </style>
            `;

            this.orderInfoContainer.style.position = 'relative';
            this.orderInfoContainer.appendChild(loadingDiv);
        }
    }

    // Скрывает индикатор загрузки
    hideLoadingIndicator() {
        const loadingDiv = document.getElementById('force-update-loading');
        if (loadingDiv) {
            loadingDiv.remove();
        }
    }

    // Добавляет кнопку сброса активации в правый нижний угол
    addResetButtonToCorner() {
        // Удаляем предыдущую кнопку если есть
        const existingButton = document.getElementById('group-order-reset-button');
        if (existingButton) {
            existingButton.remove();
        }

        const resetButton = document.createElement('button');
        resetButton.id = 'group-order-reset-button';
        resetButton.innerHTML = '⚙️';
        resetButton.title = 'Сбросить активацию плагина';
        resetButton.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            background: var(--button-danger-background);
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            opacity: 0.7;
            z-index: 9999;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        `;

        resetButton.addEventListener('click', () => {
            this.resetActivation();
        });

        resetButton.addEventListener('mouseenter', () => {
            resetButton.style.opacity = '1';
            resetButton.style.transform = 'scale(1.1)';
            resetButton.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.4)';
        });

        resetButton.addEventListener('mouseleave', () => {
            resetButton.style.opacity = '0.7';
            resetButton.style.transform = 'scale(1)';
            resetButton.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)';
        });

        document.body.appendChild(resetButton);
    }

    // Удаляет кнопку сброса из угла
    removeResetButtonFromCorner() {
        const resetButton = document.getElementById('group-order-reset-button');
        if (resetButton) {
            resetButton.remove();
        }
    }

    // Создает блок кнопок внизу
    createBottomButtonsContainer(orderData) {
        const container = document.createElement('div');
        container.style.cssText = `
            margin-bottom: 8px;
            padding: 8px;
            background-color: var(--background-secondary);
            border-radius: 6px;
            border: 1px solid var(--background-modifier-accent);
            display: flex;
            flex-direction: column;
            gap: 6px;
        `;

        // Первая строка кнопок
        const firstRow = document.createElement('div');
        firstRow.style.cssText = `
            display: flex;
            gap: 6px;
            justify-content: center;
            flex-wrap: wrap;
        `;

        // Кнопка "Время стартов"
        const sendTimeButton = document.createElement('button');
        sendTimeButton.innerHTML = '⏰ Время стартов';
        sendTimeButton.style.cssText = `
            padding: 4px 8px;
            background: var(--green-360);
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 10px;
            cursor: pointer;
            transition: all 0.2s;
            font-weight: 500;
            flex: 1;
            min-width: 0;
        `;

        sendTimeButton.addEventListener('click', () => {
            this.sendStartTimes(orderData);
        });

        sendTimeButton.addEventListener('mouseenter', () => {
            sendTimeButton.style.background = '#2d7d32';
            sendTimeButton.style.transform = 'scale(1.05)';
        });

        sendTimeButton.addEventListener('mouseleave', () => {
            sendTimeButton.style.background = 'var(--green-360)';
            sendTimeButton.style.transform = 'scale(1)';
        });

        // Кнопка "First message"
        const firstMessageButton = document.createElement('button');
        firstMessageButton.innerHTML = '📝 First message';
        firstMessageButton.style.cssText = `
            padding: 4px 8px;
            background: #5865f2;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 10px;
            cursor: pointer;
            transition: all 0.2s;
            font-weight: 500;
            flex: 1;
            min-width: 0;
        `;

        firstMessageButton.addEventListener('click', () => {
            this.sendFirstMessage(orderData);
        });

        firstMessageButton.addEventListener('mouseenter', () => {
            firstMessageButton.style.background = '#4752c4';
            firstMessageButton.style.transform = 'scale(1.05)';
        });

        firstMessageButton.addEventListener('mouseleave', () => {
            firstMessageButton.style.background = '#5865f2';
            firstMessageButton.style.transform = 'scale(1)';
        });

        // Кнопка сброса активации
        const resetButton = document.createElement('button');
        resetButton.innerHTML = '⚙️ Сброс';
        resetButton.title = 'Сбросить активацию плагина';
        resetButton.style.cssText = `
            padding: 4px 8px;
            background: var(--button-danger-background);
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 10px;
            cursor: pointer;
            transition: all 0.2s;
            font-weight: 500;
            flex: 1;
            min-width: 0;
        `;

        resetButton.addEventListener('click', () => {
            this.resetActivation();
        });

        resetButton.addEventListener('mouseenter', () => {
            resetButton.style.background = '#a12d2a';
            resetButton.style.transform = 'scale(1.05)';
        });

        resetButton.addEventListener('mouseleave', () => {
            resetButton.style.background = 'var(--button-danger-background)';
            resetButton.style.transform = 'scale(1)';
        });

        firstRow.appendChild(sendTimeButton);
        firstRow.appendChild(firstMessageButton);
        firstRow.appendChild(resetButton);

        // Вторая строка кнопок
        const secondRow = document.createElement('div');
        secondRow.style.cssText = `
            display: flex;
            gap: 6px;
            justify-content: center;
            flex-wrap: wrap;
        `;

        // Кнопка "Normal raids"
        const normalRaidsButton = document.createElement('button');
        normalRaidsButton.innerHTML = '🛡️ Normal raids';
        normalRaidsButton.style.cssText = `
            padding: 4px 8px;
            background: var(--yellow-300);
            color: var(--text-normal);
            border: none;
            border-radius: 4px;
            font-size: 10px;
            cursor: pointer;
            transition: all 0.2s;
            font-weight: 500;
            flex: 1;
            min-width: 0;
        `;

        normalRaidsButton.addEventListener('click', async () => {
            await this.sendAvailableRaidsWithLoading(normalRaidsButton, 'normal');
        });

        normalRaidsButton.addEventListener('mouseenter', () => {
            normalRaidsButton.style.background = '#f9ca24';
            normalRaidsButton.style.transform = 'scale(1.05)';
        });

        normalRaidsButton.addEventListener('mouseleave', () => {
            normalRaidsButton.style.background = 'var(--yellow-300)';
            normalRaidsButton.style.transform = 'scale(1)';
        });

        // Кнопка "Heroic Raids"
        const heroicRaidsButton = document.createElement('button');
        heroicRaidsButton.innerHTML = '⚔️ Heroic Raids';
        heroicRaidsButton.style.cssText = `
            padding: 4px 8px;
            background: var(--red-400);
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 10px;
            cursor: pointer;
            transition: all 0.2s;
            font-weight: 500;
            flex: 1;
            min-width: 0;
        `;

        heroicRaidsButton.addEventListener('click', async () => {
            await this.sendAvailableRaidsWithLoading(heroicRaidsButton, 'heroic');
        });

        heroicRaidsButton.addEventListener('mouseenter', () => {
            heroicRaidsButton.style.background = '#c0392b';
            heroicRaidsButton.style.transform = 'scale(1.05)';
        });

        heroicRaidsButton.addEventListener('mouseleave', () => {
            heroicRaidsButton.style.background = 'var(--red-400)';
            heroicRaidsButton.style.transform = 'scale(1)';
        });

        // Кнопка "Mythic Raids"
        const mythicRaidsButton = document.createElement('button');
        mythicRaidsButton.innerHTML = '🔥 Mythic Raids';
        mythicRaidsButton.style.cssText = `
            padding: 4px 8px;
            background: #8b5cf6;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 10px;
            cursor: pointer;
            transition: all 0.2s;
            font-weight: 500;
            flex: 1;
            min-width: 0;
        `;

        mythicRaidsButton.addEventListener('click', async () => {
            await this.sendAvailableRaidsWithLoading(mythicRaidsButton, 'mythic');
        });

        mythicRaidsButton.addEventListener('mouseenter', () => {
            mythicRaidsButton.style.background = '#7c3aed';
            mythicRaidsButton.style.transform = 'scale(1.05)';
        });

        mythicRaidsButton.addEventListener('mouseleave', () => {
            mythicRaidsButton.style.background = '#8b5cf6';
            mythicRaidsButton.style.transform = 'scale(1)';
        });

        secondRow.appendChild(normalRaidsButton);
        secondRow.appendChild(heroicRaidsButton);
        secondRow.appendChild(mythicRaidsButton);

        container.appendChild(firstRow);
        container.appendChild(secondRow);

        return container;
    }

    // Отмечает поле как измененное и показывает кнопку сохранения
    markFieldAsChanged(input) {
        // Ищем родительскую строку более надежным способом
        let row = input.closest('.editable-field');
        if (row) {
            row = row.parentElement.parentElement;
        } else {
            // Если не нашли через .editable-field, ищем через другие селекторы
            row = input.closest('div[style*="margin: 6px 0"]') ||
                  input.closest('div[style*="background-color"]') ||
                  input.closest('div').parentElement;
        }

        if (row && row.saveButton) {
            // Показываем кнопку сохранения
            row.saveButton.style.display = 'flex';
            row.saveButton.style.opacity = '0.8';

            // Добавляем визуальную индикацию изменения
            if (input.tagName === 'SELECT') {
                input.style.borderColor = 'var(--yellow-300)';
                input.style.background = 'rgba(255, 255, 0, 0.1)';
            } else {
                input.style.borderColor = 'var(--yellow-300)';
                input.style.background = 'rgba(255, 255, 0, 0.1)';
            }

            this.debug('Поле изменено, показана кнопка сохранения для:', input.dataset.field);
        } else {
            this.debug('Не удалось найти строку с кнопкой сохранения для поля:', input.dataset.field);
        }
    }

    // Сохраняет изменения полей в Google Sheets
    async saveFieldChanges(row, rowNumber) {
        this.debug(`Сохранение изменений для строки ${rowNumber}`);

        try {
            // Собираем все измененные поля
            const changes = {};
            const inputs = row.querySelectorAll('input, textarea, select');

            this.debug(`Найдено полей для проверки: ${inputs.length}`);

            inputs.forEach((input, index) => {
                const field = input.dataset.field;
                const originalValue = input.dataset.originalValue;
                let currentValue = input.value;

                this.debug(`Поле ${index + 1}: ${input.tagName} field="${field}" original="${originalValue}" current="${currentValue}"`);

                // Конвертируем дату обратно в формат DD.MM.YYYY если это поле даты
                if (field === 'date' && currentValue && currentValue.includes('-')) {
                    const parts = currentValue.split('-');
                    if (parts.length === 3) {
                        currentValue = `${parts[2]}.${parts[1]}.${parts[0]}`;
                    }
                }

                // Для сравнения также конвертируем оригинальное значение даты в тот же формат
                let compareOriginalValue = originalValue;
                if (field === 'date' && originalValue && originalValue.includes('.')) {
                    // Оригинальное значение уже в формате DD.MM.YYYY, оставляем как есть
                    compareOriginalValue = originalValue;
                } else if (field === 'date' && originalValue && originalValue.includes('-')) {
                    // Если оригинальное значение почему-то в формате YYYY-MM-DD, конвертируем
                    const parts = originalValue.split('-');
                    if (parts.length === 3) {
                        compareOriginalValue = `${parts[2]}.${parts[1]}.${parts[0]}`;
                    }
                }

                if (currentValue !== compareOriginalValue) {
                    changes[field] = currentValue;
                    this.debug(`✓ Изменение: ${field} = "${currentValue}" (было: "${compareOriginalValue}")`);
                } else {
                    this.debug(`- Без изменений: ${field} = "${currentValue}"`);
                }
            });

            this.debug('Собранные изменения:', changes);
            this.debug('Количество изменений:', Object.keys(changes).length);

            if (Object.keys(changes).length === 0) {
                this.showNotification('Нет изменений для сохранения', 'info');
                return;
            }

            // Показываем индикатор загрузки
            this.showSaveIndicator(row, true);

            // Отправляем изменения на сервер (используем базовый URL без /api/sheets)
            const baseUrl = this.SHEETS_CONFIG.PROXY_URL.replace('/api/sheets', '');
            const response = await fetch(`${baseUrl}/api/update-row`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': this.SHEETS_CONFIG.API_TOKEN
                },
                body: JSON.stringify({
                    spreadsheetId: this.SHEETS_CONFIG.SPREADSHEET_ID,
                    sheetName: this.SHEETS_CONFIG.SHEET_NAME,
                    rowNumber: rowNumber,
                    changes: changes
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.success) {
                // Успешно сохранено
                this.showNotification('✅ Изменения сохранены', 'success');

                // Обновляем оригинальные значения
                inputs.forEach(input => {
                    input.dataset.originalValue = input.value;
                    input.style.borderColor = 'transparent';
                    input.style.background = 'transparent';
                });

                // Скрываем кнопку сохранения
                row.saveButton.style.display = 'none';

                this.debug('Изменения успешно сохранены');
            } else {
                throw new Error(result.error || 'Неизвестная ошибка сервера');
            }

        } catch (error) {
            this.error('Ошибка при сохранении изменений: ' + error.message);
            this.showNotification('❌ Ошибка сохранения: ' + error.message, 'error');
        } finally {
            this.showSaveIndicator(row, false);
        }
    }

    // Показывает/скрывает индикатор сохранения
    showSaveIndicator(row, show) {
        if (show) {
            row.saveButton.innerHTML = '⏳';
            row.saveButton.style.opacity = '1';
            row.saveButton.disabled = true;
        } else {
            row.saveButton.innerHTML = '💾';
            row.saveButton.style.opacity = '0.8';
            row.saveButton.disabled = false;
        }
    }

    // Загружает метаданные полей (validation rules) из Google Sheets
    async loadFieldMetadata(forceRefresh = false) {
        // Проверяем кеш (обновляем раз в 10 минут)
        const cacheKey = 'fieldMetadata';
        const cacheTime = this.getStorageItem(cacheKey + '_time');
        const now = Date.now();

        if (!forceRefresh && this.fieldMetadata && cacheTime && (now - cacheTime < 10 * 60 * 1000)) {
            this.debug('Используем кешированные метаданные полей');
            return this.fieldMetadata;
        }

        try {
            const baseUrl = this.SHEETS_CONFIG.PROXY_URL.replace('/api/sheets', '');
            const response = await fetch(`${baseUrl}/api/field-metadata/${this.SHEETS_CONFIG.SPREADSHEET_ID}/${this.SHEETS_CONFIG.SHEET_NAME}`, {
                headers: {
                    'X-API-Key': this.SHEETS_CONFIG.API_TOKEN
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.success) {
                this.fieldMetadata = result.metadata;
                // Сохраняем в кеш
                this.setStorageItem(cacheKey, this.fieldMetadata);
                this.setStorageItem(cacheKey + '_time', now);
                this.debug('Загружены и кешированы метаданные полей:', this.fieldMetadata);
                return this.fieldMetadata;
            } else {
                throw new Error(result.error || 'Неизвестная ошибка');
            }

        } catch (error) {
            this.error('Ошибка загрузки метаданных полей: ' + error.message);
            // Возвращаем базовые метаданные по умолчанию
            this.fieldMetadata = {
                date: { type: 'date', options: [] },
                time: { type: 'text', options: [] },
                task: { type: 'text', options: [] },
                nickname: { type: 'text', options: [] },
                composition: {
                    type: 'select',
                    options: [
                'A1',
                'H1',
                'A2s',
                'H2s'
                    ]
                },
                link: { type: 'url', options: [] }
            };
            return this.fieldMetadata;
        }
    }

    // Безопасный показ уведомлений
    showToast(content, options = {}) {
        if (BdApi.UI && BdApi.UI.showToast) {
            BdApi.UI.showToast(content, options);
        } else if (BdApi.showToast) {
            BdApi.showToast(content, options);
        } else {
            this.log(content, options.type || 'info');
        }
    }

    load() {
        this.log("Загрузка плагина...");
        // Инициализируем модули Discord при загрузке
        this.initDiscordModules();
    }

    // Конфигурация для Google Sheets API
    get SHEETS_CONFIG() {
        return {
            // URL прокси-сервера
            PROXY_URL: 'https://discord-6r4h.onrender.com/api/sheets',
            SPREADSHEET_ID: '1gL6WSAa0XaaOKm7hmyK7LCFG3JOul3umqhqi_hd3ElI',
            SHEET_NAME: 'Oper', // Название листа
            // API токен для аутентификации
            API_TOKEN: 'meldteamfjlsjpqpo1298d3f892' // Токен для Discord плагина
        };
    }





    start() {
        this.log('Плагин запущен');

        // Проверяем настройку debug режима из localStorage
        const debugSetting = this.getStorageItem('GroupOrderInfo_debugMode');
        if (debugSetting === 'true') {
            this.debugMode = true;
            this.info('Debug режим включен');
        }

        // Переинициализируем модули Discord при старте
        this.initDiscordModules();

        this.initializeActivationCode();
        this.startChannelListener();
        this.connectToDiscordSearchServer();
        // Инициализируем для текущего канала
        if (this.SelectedChannelStore) {
            this.currentChannelId = this.SelectedChannelStore.getChannelId();
        }
        setTimeout(() => this.updateOrderInfo(), 2000);
    }

    stop() {
        this.log('Плагин остановлен');

        // Очищаем все таймеры и запросы
        if (this.updateTimeout) {
            clearTimeout(this.updateTimeout);
            this.updateTimeout = null;
        }

        if (this.currentRequest) {
            this.currentRequest.abort();
            this.currentRequest = null;
        }

        if (this.channelCheckInterval) {
            clearInterval(this.channelCheckInterval);
            this.channelCheckInterval = null;
        }

        // Сбрасываем состояние
        this.processingChannel = null;
        this.lastProcessedOrder = null;
        this.requestQueue.clear();
        this.retryAttempts.clear();

        this.removeOrderInfo();
        this.removeResetButtonFromCorner();
        this.disconnectFromDiscordSearchServer();
    }

    // Извлекает номер заказа из названия канала
    extractOrderNumber(channelName) {
        this.log('Название канала: ' + channelName);
        if (!channelName) return null;
        const match = channelName.match(/^(\d+_\d+)/);
        const orderNumber = match ? match[1] : null;
        this.log('Извлеченный номер заказа: ' + orderNumber);
        return orderNumber;
    }

    // Получает данные из Google Sheets через прокси-сервер
    async fetchSheetData(orderNumber) {
        return this.fetchSheetDataOptimized(orderNumber);
    }

    // Оптимизированный метод получения данных с поддержкой отмены
    async fetchSheetDataOptimized(orderNumber, signal = null, forceUpdate = false) {
        this.debug('Запрос данных для номера заказа: ' + orderNumber + (forceUpdate ? ' (принудительно)' : ''));

        const startTime = Date.now();

        try {
            const config = this.SHEETS_CONFIG;
            let url = `${config.PROXY_URL}/${config.SPREADSHEET_ID}/${config.SHEET_NAME}?orderNumber=${encodeURIComponent(orderNumber)}`;

            // Добавляем параметр для игнорирования кеша сервера
           
                  if (forceUpdate) {
                url += '&forceUpdate=true&_t=' + Date.now();
            }
           

            this.debug('URL запроса к прокси: ' + url);

            const fetchOptions = {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Cache-Control': forceUpdate ? 'no-cache, no-store, must-revalidate' : 'no-cache',
                    'X-API-Key': config.API_TOKEN
                }
            };

            // Добавляем signal для отмены запроса
            if (signal) {
                fetchOptions.signal = signal;
            }

            const response = await fetch(url, fetchOptions);
            const responseTime = Date.now() - startTime;

            this.debug(`Статус ответа: ${response.status}, время: ${responseTime}ms`);

            if (!response.ok) {
                this.error(`Ошибка HTTP: ${response.status} ${response.statusText}`);
                const errorText = await response.text();
                this.debug('Текст ошибки: ' + errorText);
                return [];
            }

            const result = await response.json();
            this.debug('Ответ от прокси: ' + JSON.stringify(result));

            if (!result.success) {
                this.error('Ошибка от прокси: ' + result.error);
                return [];
            }

            this.info(`Найдено записей: ${result.data.length} (${responseTime}ms)`);
            return result.data;

        } catch (error) {
            if (error.name === 'AbortError') {
                this.debug('Запрос отменен: ' + orderNumber);
                return [];
            }
            this.error('Ошибка при получении данных из Google Sheets: ' + error.message);
            return [];
        }
    }

                // Создает элемент с информацией о заказе
                async createOrderInfoElement(orderData) {
                    // Загружаем метаданные полей перед созданием элементов
                    await this.loadFieldMetadata();
                    const container = document.createElement('div');
                    container.className = 'group-order-info';
                    container.style.cssText = `
                        margin: 8px 12px;
                        display: flex;
                        flex-direction: column;
                    `;

                    // Создаем прокручиваемый блок для заказов
                    const ordersContainer = document.createElement('div');
                    ordersContainer.className = 'orders-scrollable';
                    ordersContainer.style.cssText = `
                        padding: 12px;
                        background-color: var(--background-primary);
                        border-radius: 8px;
                        border-left: 4px solid var(--brand-experiment);
                        font-size: 12px;
                        color: var(--header-primary);
                        box-shadow: 0 2px 6px rgba(0,0,0,0.2);
                        border: 1px solid var(--background-modifier-accent);
                        max-height: calc(100vh - 400px);
                        overflow-y: auto;
                    `;

                    if (orderData.length === 0) {
                        // Создаем заголовок с кнопкой обновления даже когда нет данных
                        const title = document.createElement('div');
                        title.style.cssText = 'font-weight: 600; margin-bottom: 8px; color: var(--brand-experiment); font-size: 13px; display: flex; justify-content: space-between; align-items: center;';
                        title.innerHTML = `<span>📋 Заказ (0 записей)</span>`;

                        // Контейнер для кнопок
                        const buttonsContainer = document.createElement('div');
                        buttonsContainer.style.cssText = 'display: flex; gap: 4px; align-items: center;';

                        // Добавляем кнопку принудительного обновления
                        const forceUpdateButton = document.createElement('button');
                        forceUpdateButton.innerHTML = '🔄';
                        forceUpdateButton.title = 'Принудительно обновить данные (игнорируя кеш)';
                        forceUpdateButton.style.cssText = `
                            padding: 2px 6px;
                            background: var(--brand-experiment);
                            color: white;
                            border: none;
                            border-radius: 3px;
                            font-size: 9px;
                            cursor: pointer;
                            transition: all 0.2s;
                            opacity: 0.8;
                        `;

                        forceUpdateButton.addEventListener('click', () => {
                            this.forceUpdateOrderInfo();
                        });

                        forceUpdateButton.addEventListener('mouseenter', () => {
                            forceUpdateButton.style.opacity = '1';
                            forceUpdateButton.style.transform = 'scale(1.1)';
                        });

                        forceUpdateButton.addEventListener('mouseleave', () => {
                            forceUpdateButton.style.opacity = '0.8';
                            forceUpdateButton.style.transform = 'scale(1)';
                        });

                        buttonsContainer.appendChild(forceUpdateButton);
                        title.appendChild(buttonsContainer);
                        ordersContainer.appendChild(title);

                        // Добавляем сообщение о том, что данные не найдены
                        const noDataMessage = document.createElement('div');
                        noDataMessage.style.cssText = 'color: var(--header-secondary); text-align: center; padding: 8px;';
                        noDataMessage.textContent = 'Информация о заказе не найдена';
                        ordersContainer.appendChild(noDataMessage);

                        container.appendChild(ordersContainer);
                        return container;
                    }

                    const title = document.createElement('div');
                    title.style.cssText = 'font-weight: 600; margin-bottom: 8px; color: var(--brand-experiment); font-size: 13px; display: flex; justify-content: space-between; align-items: center;';
                    title.innerHTML = `<span>📋 Заказ (${orderData.length} записей)</span>`;

                    // Контейнер для кнопок
                    const buttonsContainer = document.createElement('div');
                    buttonsContainer.style.cssText = 'display: flex; gap: 4px; align-items: center;';

                    // Добавляем кнопку принудительного обновления
                    const forceUpdateButton = document.createElement('button');
                    forceUpdateButton.innerHTML = '🔄';
                    forceUpdateButton.title = 'Принудительно обновить данные (игнорируя кеш)';
                    forceUpdateButton.style.cssText = `
                        padding: 2px 6px;
                        background: var(--brand-experiment);
                        color: white;
                        border: none;
                        border-radius: 3px;
                        font-size: 9px;
                        cursor: pointer;
                        transition: all 0.2s;
                        opacity: 0.8;
                    `;

                    forceUpdateButton.addEventListener('click', () => {
                        this.forceUpdateOrderInfo();
                    });

                    forceUpdateButton.addEventListener('mouseenter', () => {
                        forceUpdateButton.style.opacity = '1';
                        forceUpdateButton.style.transform = 'scale(1.1)';
                    });

                    forceUpdateButton.addEventListener('mouseleave', () => {
                        forceUpdateButton.style.opacity = '0.8';
                        forceUpdateButton.style.transform = 'scale(1)';
                    });

                    buttonsContainer.appendChild(forceUpdateButton);
                    title.appendChild(buttonsContainer);
                    ordersContainer.appendChild(title);

                    orderData.forEach((data) => {
                        const row = document.createElement('div');

                        // Используем готовое поле isHighlighted с сервера или проверяем AR
                        const isHighlighted = data.isHighlighted || (data.ar && (
                            data.ar.toString().toLowerCase() === 'true' ||
                            data.ar === 'TRUE' ||
                            data.ar === '1' ||
                            data.ar === 1 ||
                            data.ar === true
                        ));

                        console.log('[GroupOrderInfo] AR значение:', data.ar, 'isHighlighted:', data.isHighlighted, 'итоговая подсветка:', isHighlighted);

                        const backgroundColor = isHighlighted ? '#4a2c3a' : 'var(--background-secondary)';
                        const textColor = isHighlighted ? '#e8c5d4' : 'var(--header-primary)';
                        const labelColor = isHighlighted ? '#d4a5b8' : 'var(--header-secondary)';
                        const borderColor = isHighlighted ? '#6b3d52' : 'var(--brand-experiment)';

                        row.style.cssText = `
                            margin: 6px 0;
                            padding: 10px;
                            background-color: ${backgroundColor};
                            border-radius: 8px;
                            border-left: 3px solid ${borderColor};
                            border: 1px solid ${isHighlighted ? '#6b3d52' : 'var(--background-modifier-accent)'};
                            position: relative;
                            font-size: 15px;
                            line-height: 1.4;
                        `;

                        // Вертикальная компоновка с увеличенным шрифтом
                        const rowContent = document.createElement('div');
                        rowContent.style.cssText = `
                            display: flex;
                            flex-direction: column;
                            gap: 6px;
                            font-size: 15px;
                        `;

                        // Функция для создания редактируемого поля с поддержкой метаданных
                        const createEditableField = (label, value, fieldType, isDateTimeRow = false) => {
                            const field = document.createElement('div');
                            field.className = 'editable-field';

                            if (isDateTimeRow) {
                                // Дата и время в одной строке (редактируемые)
                                field.style.cssText = `
                                    display: flex;
                                    justify-content: space-between;
                                    align-items: center;
                                    gap: 10px;
                                `;

                                // Поле даты с календарем
                                const dateInput = document.createElement('input');
                                dateInput.type = 'date';

                                // Конвертируем формат даты если нужно (DD.MM.YYYY -> YYYY-MM-DD)
                                let dateValue = value.date || '';
                                if (dateValue && dateValue.includes('.')) {
                                    const parts = dateValue.split('.');
                                    if (parts.length === 3) {
                                        dateValue = `${parts[2]}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`;
                                    }
                                }

                                dateInput.value = dateValue;
                                dateInput.dataset.field = 'date';
                                dateInput.dataset.originalValue = value.date || '';
                                dateInput.style.cssText = `
                                    background: transparent;
                                    border: 1px solid transparent;
                                    color: ${textColor};
                                    font-size: 13px;
                                    font-weight: 500;
                                    padding: 2px 4px;
                                    border-radius: 3px;
                                    width: 90px;
                                    transition: all 0.2s;
                                `;

                                // Поле времени - проверяем метаданные для выпадающего списка
                                const timeMetadata = this.fieldMetadata?.time;
                                let timeInput;

                                if (timeMetadata?.type === 'select' && timeMetadata.options?.length > 0) {
                                    // Создаем выпадающий список для времени
                                    timeInput = document.createElement('select');

                                    // Добавляем пустой вариант
                                    const emptyOption = document.createElement('option');
                                    emptyOption.value = '';
                                    emptyOption.textContent = '—';
                                    timeInput.appendChild(emptyOption);

                                    // Проверяем, есть ли текущее значение времени в списке опций
                                    const currentTimeValue = value.time || '';
                                    let timeValueFoundInOptions = false;

                                    // Добавляем варианты из метаданных
                                    timeMetadata.options.forEach(option => {
                                        const optionElement = document.createElement('option');
                                        const optionValue = typeof option === 'string' ? option : (option.userEnteredValue || option);
                                        optionElement.value = optionValue;
                                        optionElement.textContent = optionValue;
                                        if (optionElement.value === currentTimeValue) {
                                            optionElement.selected = true;
                                            timeValueFoundInOptions = true;
                                        }
                                        timeInput.appendChild(optionElement);
                                    });

                                    // Если текущее значение времени не найдено в опциях, добавляем его
                                    if (currentTimeValue && !timeValueFoundInOptions) {
                                        const customTimeOption = document.createElement('option');
                                        customTimeOption.value = currentTimeValue;
                                        customTimeOption.textContent = currentTimeValue;
                                        customTimeOption.selected = true;
                                        customTimeOption.style.fontStyle = 'italic';
                                        customTimeOption.style.color = '#ffa500';
                                        timeInput.appendChild(customTimeOption);
                                    }
                                } else {
                                    // Обычное текстовое поле
                                    timeInput = document.createElement('input');
                                    timeInput.type = 'text';
                                    timeInput.value = value.time || '';
                                }

                                timeInput.dataset.field = 'time';
                                timeInput.dataset.originalValue = value.time || '';
                                timeInput.style.cssText = `
                                    background: ${timeInput.tagName === 'SELECT' ? 'var(--background-secondary-alt)' : 'transparent'};
                                    border: 1px solid ${timeInput.tagName === 'SELECT' ? 'var(--background-modifier-accent)' : 'transparent'};
                                    color: ${textColor};
                                    font-size: 13px;
                                    font-weight: 500;
                                    padding: 2px 4px;
                                    border-radius: 3px;
                                    width: 70px;
                                    text-align: right;
                                    transition: all 0.2s;
                                `;

                                // Добавляем обработчики для подсветки при фокусе
                                [dateInput, timeInput].forEach(input => {
                                    input.addEventListener('focus', () => {
                                        input.style.border = `1px solid ${borderColor}`;
                                        if (input.tagName === 'SELECT') {
                                            input.style.background = 'var(--background-secondary-alt)';
                                        } else {
                                            input.style.background = isHighlighted ? 'rgba(255,255,255,0.1)' : 'var(--background-primary)';
                                        }
                                    });

                                    input.addEventListener('blur', () => {
                                        if (input.tagName === 'SELECT') {
                                            input.style.border = '1px solid var(--background-modifier-accent)';
                                            input.style.background = 'var(--background-secondary-alt)';
                                        } else {
                                            input.style.border = '1px solid transparent';
                                            input.style.background = 'transparent';
                                        }
                                        // Проверяем изменения
                                        if (input.value !== input.dataset.originalValue) {
                                            this.markFieldAsChanged(input);
                                        }
                                    });

                                    // Для select элементов добавляем обработчик change
                                    if (input.tagName === 'SELECT') {
                                        input.addEventListener('change', () => {
                                            if (input.value !== input.dataset.originalValue) {
                                                this.markFieldAsChanged(input);
                                            }
                                        });
                                    }
                                });

                                field.appendChild(dateInput);
                                field.appendChild(timeInput);
                            } else {
                                // Обычное редактируемое поле
                                field.innerHTML = `
                                    <div style="color: ${labelColor}; font-size: 12px; font-weight: 600; margin-bottom: 2px;">${label}</div>
                                `;

                                // Проверяем метаданные для создания соответствующего элемента
                                const fieldMetadata = this.fieldMetadata?.[fieldType];
                                let input;

                                if (fieldMetadata?.type === 'select' && fieldMetadata.options?.length > 0) {
                                    // Создаем выпадающий список
                                    input = document.createElement('select');

                                    // Добавляем пустой вариант
                                    const emptyOption = document.createElement('option');
                                    emptyOption.value = '';
                                    emptyOption.textContent = '—';
                                    input.appendChild(emptyOption);

                                    // Проверяем, есть ли текущее значение в списке опций
                                    const currentValue = value || '';
                                    let valueFoundInOptions = false;

                                    // Добавляем варианты из метаданных
                                    fieldMetadata.options.forEach(option => {
                                        const optionElement = document.createElement('option');
                                        const optionValue = typeof option === 'string' ? option : (option.userEnteredValue || option);
                                        optionElement.value = optionValue;
                                        optionElement.textContent = optionValue;
                                        if (optionElement.value === currentValue) {
                                            optionElement.selected = true;
                                            valueFoundInOptions = true;
                                        }
                                        input.appendChild(optionElement);
                                    });

                                    // Если текущее значение не найдено в опциях, добавляем его
                                    if (currentValue && !valueFoundInOptions) {
                                        const customOption = document.createElement('option');
                                        customOption.value = currentValue;
                                        customOption.textContent = currentValue;
                                        customOption.selected = true;
                                        customOption.style.fontStyle = 'italic';
                                        customOption.style.color = '#ffa500';
                                        input.appendChild(customOption);
                                    }

                                    input.style.cssText = `
                                        background: var(--background-secondary-alt);
                                        border: 1px solid var(--background-modifier-accent);
                                        color: ${textColor};
                                        font-size: 15px;
                                        padding: 2px 4px;
                                        border-radius: 3px;
                                        width: ${fieldType === 'composition' ? '50%' : '100%'};
                                        transition: all 0.2s;
                                    `;

                                    // Применяем глобальные стили для всех select элементов
                                    if (!document.getElementById('discord-select-global-styles')) {
                                        const globalStyle = document.createElement('style');
                                        globalStyle.id = 'discord-select-global-styles';
                                        globalStyle.textContent = `
                                            select {
                                                background: var(--background-secondary-alt) !important;
                                                color: var(--text-normal) !important;
                                                border: 1px solid var(--background-modifier-accent) !important;
                                            }
                                            select:focus {
                                                border-color: var(--brand-experiment) !important;
                                                outline: none !important;
                                                background: var(--background-secondary-alt) !important;
                                            }
                                            select option {
                                                background: var(--background-secondary-alt) !important;
                                                color: var(--text-normal) !important;
                                            }
                                        `;
                                        document.head.appendChild(globalStyle);
                                    }
                                } else {
                                    // Создаем обычное поле (input или textarea)
                                    input = document.createElement((fieldType === 'link' || fieldType === 'nickname') ? 'input' : 'textarea');
                                    input.value = value || '';
                                }

                                input.dataset.field = fieldType;
                                input.dataset.originalValue = value || '';

                                // Применяем стили только для не-select элементов
                                if (input.tagName !== 'SELECT') {
                                    if (fieldType === 'link') {
                                        input.type = 'url';
                                        input.style.cssText = `
                                            background: transparent;
                                            border: 1px solid transparent;
                                            color: ${textColor};
                                            font-size: 15px;
                                            padding: 2px 4px;
                                            border-radius: 3px;
                                            width: 100%;
                                            word-break: break-all;
                                            transition: all 0.2s;
                                            resize: none;
                                        `;
                                    } else if (fieldType === 'nickname') {
                                        input.type = 'text';
                                        input.style.cssText = `
                                            background: transparent;
                                            border: 1px solid transparent;
                                            color: ${textColor};
                                            font-size: 15px;
                                            padding: 2px 4px;
                                            border-radius: 3px;
                                            width: 100%;
                                            transition: all 0.2s;
                                        `;
                                    } else {
                                        input.style.cssText = `
                                            background: transparent;
                                            border: 1px solid transparent;
                                            color: ${textColor};
                                            font-size: 15px;
                                            padding: 2px 4px;
                                            border-radius: 3px;
                                            width: 100%;
                                            min-height: 20px;
                                            resize: none;
                                            font-family: inherit;
                                            line-height: 1.3;
                                            transition: all 0.2s;
                                        `;

                                        // Автоматическое изменение высоты для textarea
                                        input.addEventListener('input', () => {
                                            input.style.height = 'auto';
                                            input.style.height = input.scrollHeight + 'px';
                                        });
                                    }
                                }

                                // Обработчики фокуса и изменений
                                input.addEventListener('focus', () => {
                                    input.style.border = `1px solid ${borderColor}`;
                                    if (input.tagName === 'SELECT') {
                                        input.style.background = 'var(--background-secondary-alt)';
                                    } else {
                                        input.style.background = isHighlighted ? 'rgba(255,255,255,0.1)' : 'var(--background-primary)';
                                    }
                                });

                                input.addEventListener('blur', () => {
                                    if (input.tagName === 'SELECT') {
                                        input.style.border = '1px solid var(--background-modifier-accent)';
                                        input.style.background = 'var(--background-secondary-alt)';
                                    } else {
                                        input.style.border = '1px solid transparent';
                                        input.style.background = 'transparent';
                                    }
                                    // Проверяем изменения
                                    if (input.value !== input.dataset.originalValue) {
                                        this.markFieldAsChanged(input);
                                    }
                                });

                                // Для select элементов добавляем обработчик change
                                if (input.tagName === 'SELECT') {
                                    input.addEventListener('change', () => {
                                        if (input.value !== input.dataset.originalValue) {
                                            this.markFieldAsChanged(input);
                                        }
                                    });
                                }

                                // Для полей nickname и link добавляем обработчик Enter
                                if (fieldType === 'nickname' || fieldType === 'link') {
                                    input.addEventListener('keydown', (e) => {
                                        if (e.key === 'Enter') {
                                            e.preventDefault(); // Предотвращаем создание новой строки
                                            input.blur(); // Завершаем редактирование (вызывает blur event)
                                        }
                                    });
                                }

                                field.appendChild(input);
                            }

                            return field;
                        };

                        // Добавляем редактируемые поля в вертикальном формате
                        // 1. Дата и время в одной строке (редактируемые)
                        rowContent.appendChild(createEditableField('', { date: data.b, time: data.t }, 'datetime', true));

                        // 2. Задача на отдельной строке (редактируемая)
                        rowContent.appendChild(createEditableField('🎯 Task', data.v, 'task'));

                        // 3. Никнейм на отдельной строке (редактируемый)
                        rowContent.appendChild(createEditableField('👤 Nickname', data.q, 'nickname'));
    
                        // 4. Ссылка на отдельной строке (редактируемая)
                        rowContent.appendChild(createEditableField('🔗 Link', data.o || '', 'link'));

                        // 5. Состав на отдельной строке (редактируемый с выпадающим списком)
                        rowContent.appendChild(createEditableField('⚔️ Состав', data.composition || '', 'composition'));



                        // Создаем контейнер для кнопок в правом нижнем углу
                        const buttonsContainer = document.createElement('div');
                        buttonsContainer.style.cssText = `
                            position: absolute;
                            bottom: 6px;
                            right: 6px;
                            display: flex;
                            gap: 4px;
                            z-index: 10;
                        `;

                        // Кнопка поиска
                        const searchButton = document.createElement('button');
                        searchButton.innerHTML = '🔍';
                        searchButton.title = 'Найти в таблице';
                        searchButton.style.cssText = `
                            width: 24px;
                            height: 24px;
                            padding: 0;
                            background: var(--brand-experiment);
                            color: white;
                            border: none;
                            border-radius: 50%;
                            font-size: 12px;
                            cursor: pointer;
                            transition: all 0.2s;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            opacity: 0.8;
                        `;

                        // Кнопка сохранения изменений
                        const saveButton = document.createElement('button');
                        saveButton.innerHTML = '💾';
                        saveButton.title = 'Сохранить изменения';
                        saveButton.style.cssText = `
                            width: 24px;
                            height: 24px;
                            padding: 0;
                            background: var(--green-360);
                            color: white;
                            border: none;
                            border-radius: 50%;
                            font-size: 12px;
                            cursor: pointer;
                            transition: all 0.2s;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            opacity: 0.6;
                        `;

                        // Изначально кнопка сохранения скрыта
                        saveButton.style.display = 'none';

                        // Обработчики для кнопки поиска
                        searchButton.addEventListener('click', () => {
                            this.findOrderInSheet(this.lastProcessedOrder, data.rowNumber);
                        });

                        searchButton.addEventListener('mouseenter', () => {
                            searchButton.style.background = '#4752c4';
                            searchButton.style.opacity = '1';
                            searchButton.style.transform = 'scale(1.1)';
                        });

                        searchButton.addEventListener('mouseleave', () => {
                            searchButton.style.background = 'var(--brand-experiment)';
                            searchButton.style.opacity = '0.8';
                            searchButton.style.transform = 'scale(1)';
                        });

                        // Обработчики для кнопки сохранения
                        saveButton.addEventListener('click', () => {
                            this.saveFieldChanges(row, data.rowNumber);
                        });

                        saveButton.addEventListener('mouseenter', () => {
                            saveButton.style.background = '#43b581';
                            saveButton.style.opacity = '1';
                            saveButton.style.transform = 'scale(1.1)';
                        });

                        saveButton.addEventListener('mouseleave', () => {
                            saveButton.style.background = 'var(--green-360)';
                            saveButton.style.opacity = '0.8';
                            saveButton.style.transform = 'scale(1)';
                        });

                        // Добавляем кнопки в контейнер
                        buttonsContainer.appendChild(searchButton);
                        buttonsContainer.appendChild(saveButton);

                        // Сохраняем ссылку на кнопку сохранения для показа/скрытия
                        row.saveButton = saveButton;
                        row.dataset.rowNumber = data.rowNumber;

                        row.appendChild(rowContent);
                        row.appendChild(buttonsContainer);
                        ordersContainer.appendChild(row);
                    });

                    // Добавляем блок кнопок сверху (фиксированный)
                    const bottomButtonsContainer = this.createBottomButtonsContainer(orderData);
                    container.appendChild(bottomButtonsContainer);

                    // Добавляем прокручиваемый блок с заказами в основной контейнер
                    container.appendChild(ordersContainer);

                    return container;
                }

                // Оптимизированное обновление информации о заказе с debouncing
                async updateOrderInfo(forceUpdate = false) {
                    // Отменяем предыдущий таймер
                    if (this.updateTimeout) {
                        clearTimeout(this.updateTimeout);
                        this.updateTimeout = null;
                    }

                    // Если не принудительное обновление, добавляем debouncing
                    if (!forceUpdate) {
                        this.updateTimeout = setTimeout(() => {
                            this.updateOrderInfo(true);
                        }, 300); // 300ms debouncing
                        return;
                    }

                    // Проверяем, не обрабатывается ли уже этот канал
                    if (this.processingChannel === this.currentChannelId) {
                        this.debug('Канал уже обрабатывается: ' + this.currentChannelId);
                        return;
                    }

                    // Отменяем предыдущий запрос если есть
                    if (this.currentRequest) {
                        this.currentRequest.abort();
                        this.currentRequest = null;
                    }

                    this.processingChannel = this.currentChannelId;

                    // Если плагин не активирован, показываем код активации
                    if (!this.isActivated) {
                        this.debug('Плагин не активирован, показываем код');
                        this.showActivationCode();
                        this.processingChannel = null;
                        return;
                    }

                    this.debug('Обновление информации для канала: ' + this.currentChannelId);

                    try {
                        if (!this.ChannelStore) {
                            this.log('ChannelStore не инициализирован', 'error');
                            return;
                        }

                        const channel = this.ChannelStore.getChannel(this.currentChannelId);
                        this.debug('Канал: ' + JSON.stringify(channel));

                        if (!channel) {
                            this.log('Канал не найден');
                            return;
                        }

                        this.debug('Тип канала: ' + channel.type + ', Название: ' + channel.name);

                        // Проверяем разные типы каналов (групповые чаты могут иметь разные типы)
                        // Тип 1 - DM, тип 3 - групповой чат, тип 0 - текстовый канал сервера
                        if (channel.type !== 3 && channel.type !== 0) {
                            this.debug('Неподходящий тип канала');
                            this.removeOrderInfo();
                            this.lastProcessedOrder = null;
                            return;
                        }

                        const orderNumber = this.extractOrderNumber(channel.name);
                        if (!orderNumber) {
                            this.debug('Номер заказа не найден в названии канала');
                            this.removeOrderInfo();
                            this.lastProcessedOrder = null;
                            return;
                        }

                        // Проверяем, не обрабатывали ли мы уже этот заказ для этого канала
                        const cacheKey = `${this.currentChannelId}_${orderNumber}`;
                        if (this.lastProcessedOrder === cacheKey) {
                            this.debug('Заказ уже обработан: ' + orderNumber);
                            this.processingChannel = null;
                            return;
                        }

                        this.info('Получение данных для заказа: ' + orderNumber);

                        // Создаем AbortController для возможности отмены запроса
                        const abortController = new AbortController();
                        this.currentRequest = abortController;

                        const orderData = await this.fetchSheetDataOptimized(orderNumber, abortController.signal, false);

                        // Проверяем, не сменился ли канал во время запроса
                        if (this.processingChannel !== this.currentChannelId) {
                            this.debug('Канал сменился во время запроса, отменяем обработку');
                            this.processingChannel = null;
                            return;
                        }
                        const orderInfoElement = await this.createOrderInfoElement(orderData);

                        this.removeOrderInfo();

                        // Находим место для вставки (под списком пользователей)
                        let sidebar = document.querySelector('[class*="members_"]');
                        if (!sidebar) {
                            sidebar = document.querySelector('[class*="members-"]');
                        }
                        if (!sidebar) {
                            sidebar = document.querySelector('div[class*="members"]');
                        }

                        this.debug('Найден sidebar: ' + !!sidebar);
                        this.debug('Sidebar element: ' + sidebar);

                        if (sidebar) {
                            this.orderInfoContainer = orderInfoElement;
                            sidebar.appendChild(this.orderInfoContainer);
                            this.debug('Информация добавлена в sidebar');
                        } else {
                            // Альтернативный способ - ищем правую панель
                            const rightPanel = document.querySelector('aside');
                            if (rightPanel) {
                                this.orderInfoContainer = orderInfoElement;
                                rightPanel.appendChild(this.orderInfoContainer);
                                console.log('[GroupOrderInfo] Информация добавлена в aside');
                            } else {
                                // Последний вариант - ищем любой контейнер справа
                                const container = document.querySelector('[class*="sidebar"]');
                                if (container) {
                                    this.orderInfoContainer = orderInfoElement;
                                    container.appendChild(this.orderInfoContainer);
                                    console.log('[GroupOrderInfo] Информация добавлена в container');
                                } else {
                                    console.log('[GroupOrderInfo] Не удалось найти место для вставки');
                                }
                            }
                        }

                        // Сохраняем обработанный заказ с привязкой к каналу
                        this.lastProcessedOrder = cacheKey;
                        this.currentRequest = null;
                        this.processingChannel = null;

                    } catch (error) {
                        this.error('Ошибка при обновлении информации о заказе: ' + error.message);
                        this.currentRequest = null;
                        this.processingChannel = null;
                    }
                }

                // Отправляет запрос поиска через HTTP к серверу
                async findOrderInSheet(orderNumber, rowNumber) {
                    console.log(`[GroupOrderInfo] Запрос поиска заказа: ${orderNumber}, строка: ${rowNumber}`);

                    this.showNotification(`🔍 Переходим к заказу ${orderNumber} в строке ${rowNumber}...`, 'info');

                    try {
                        const response = await fetch('https://discord-6r4h.onrender.com/api/find-order', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-API-Key': this.SHEETS_CONFIG.API_TOKEN
                            },
                            body: JSON.stringify({
                                orderNumber: orderNumber,
                                rowNumber: rowNumber,
                                pluginId: this.pluginId
                            })
                        });

                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }

                        const result = await response.json();
                        console.log('[GroupOrderInfo] Ответ сервера:', result);

                        if (result.success) {
                            this.showNotification('✅ ' + result.message, 'success');
                        } else {
                            if (result.requiresActivation) {
                                this.showNotification('❌ Плагин не активирован. Введите код активации в расширении Chrome.', 'error');
                            } else {
                                this.showNotification('❌ ' + result.message, 'error');
                            }
                        }

                    } catch (error) {
                        console.error('[GroupOrderInfo] Ошибка запроса поиска:', error);
                        this.showNotification('❌ Ошибка связи с сервером. Запустите прокси-сервер.', 'error');
                    }
                }

                // Отправляет сообщение с временем стартов в чат
                async sendStartTimes(orderData) {
                    console.log('[GroupOrderInfo] Формируем сообщение с временем стартов');

                    try {
                        this.showNotification('⏰ Формируем сообщение с временем стартов...', 'info');

                        let message = 'Your booked spots:\n';

                        // Обрабатываем каждую запись асинхронно
                        for (const data of orderData) {
                            // Парсим данные из столбцов
                            const date = data.b || ''; // Столбец B - дата
                            const character = data.q || ''; // Столбец Q - персонаж
                            const time = data.t || ''; // Столбец T - время
                            const activity = data.v || ''; // Столбец V - активность

                            if (date && character && time && activity) {
                                // Вычисляем время до начала
                                const timeUntilStart = this.calculateTimeUntilStart(date, time);

                                // Форматируем сообщение
                                message += `${date} ${time} CET, ${activity} for character - ${character}. Invite approximately in ${timeUntilStart}\n`;
                            }
                        }

                        // Вставляем сообщение в поле ввода чата
                        await this.insertMessageIntoChat(message);

                    } catch (error) {
                        console.error('[GroupOrderInfo] Ошибка при формировании сообщения:', error);
                        this.showNotification('❌ Ошибка при формировании сообщения', 'error');
                    }
                }

                // Отправляет первое сообщение клиенту
                async sendFirstMessage(orderData) {
                    console.log('[GroupOrderInfo] Формируем первое сообщение');

                    try {
                        this.showNotification('📝 Формируем первое сообщение...', 'info');

                        if (orderData.length === 0) {
                            this.showNotification('❌ Нет данных в заказе', 'error');
                            return;
                        }

                        // Получаем текущее время CET/CEST
                        const cetNow = this.getCurrentCETTime();

                        // Фильтруем записи: исключаем те, которые начались 20 минут и более назад
                        const validEntries = orderData.filter(entry => {
                            const date = entry.b || ''; // Столбец B - дата
                            const time = entry.t || ''; // Столбец T - время

                            if (!date || !time) {
                                return false; // Пропускаем записи без даты/времени
                            }

                            try {
                                // Парсим дату и время старта
                                const [day, month, year] = date.split('.');
                                const [hours, minutes] = time.split(':');

                                // Создаем дату старта
                                const startDate = new Date();
                                startDate.setFullYear(parseInt(year), parseInt(month) - 1, parseInt(day));
                                startDate.setHours(parseInt(hours), parseInt(minutes), 0, 0);

                                // Вычисляем разность в минутах
                                const diffMs = cetNow.getTime() - startDate.getTime();
                                const diffMinutes = Math.round(diffMs / (1000 * 60));

                                // Исключаем рейды, которые начались 20 минут и более назад
                                return diffMinutes < 20;

                            } catch (error) {
                                console.error('[GroupOrderInfo] Ошибка парсинга времени для записи:', entry, error);
                                return false;
                            }
                        });

                        if (validEntries.length === 0) {
                            this.showNotification('❌ Нет подходящих рейдов (все начались более 20 минут назад)', 'error');
                            return;
                        }

                        // Выбираем ближайший стартующий рейд
                        let selectedEntry = validEntries[0];
                        let closestTimeDiff = Infinity;

                        for (const entry of validEntries) {
                            const date = entry.b || '';
                            const time = entry.t || '';

                            try {
                                // Парсим дату и время старта
                                const [day, month, year] = date.split('.');
                                const [hours, minutes] = time.split(':');

                                // Создаем дату старта
                                const startDate = new Date();
                                startDate.setFullYear(parseInt(year), parseInt(month) - 1, parseInt(day));
                                startDate.setHours(parseInt(hours), parseInt(minutes), 0, 0);

                                // Вычисляем абсолютную разность во времени
                                const timeDiff = Math.abs(startDate.getTime() - cetNow.getTime());

                                if (timeDiff < closestTimeDiff) {
                                    closestTimeDiff = timeDiff;
                                    selectedEntry = entry;
                                }

                            } catch (error) {
                                console.error('[GroupOrderInfo] Ошибка при выборе ближайшего рейда:', entry, error);
                            }
                        }

                        console.log('[GroupOrderInfo] Выбран ближайший рейд:', selectedEntry);

                        // Получаем данные из заказа
                        const date = selectedEntry.b || ''; // Столбец B - дата
                        const time = selectedEntry.t || ''; // Столбец T - время

                        if (!date || !time) {
                            this.showNotification('❌ Не удалось получить дату или время выбранного рейда', 'error');
                            return;
                        }

                        // Вычисляем Unix timestamp для Discord
                        const timestamp = this.getUnixTimestamp(date, time);

                        // Вычисляем время до начала для первого сообщения
                        const timeUntilStart = this.calculateTimeUntilStartForFirstMessage(date, time);

                        // Формируем сообщение
                        const message = this.formatFirstMessage(timestamp, timeUntilStart, time);

                        // Вставляем сообщение в поле ввода чата
                        await this.insertMessageIntoChat(message);

                        this.showNotification('✅ Первое сообщение сформировано', 'success');

                    } catch (error) {
                        console.error('[GroupOrderInfo] Ошибка при формировании первого сообщения:', error);
                        this.showNotification('❌ Ошибка при формировании первого сообщения', 'error');
                    }
                }

                // Получает Unix timestamp для Discord из даты и времени
                getUnixTimestamp(dateStr, timeStr) {
                    try {
                        // Парсим дату и время (формат: 20.07.2025 22:00)
                        const [day, month, year] = dateStr.split('.');
                        const [hours, minutes] = timeStr.split(':');

                        // Создаем дату в CET/CEST
                        const date = new Date();
                        date.setFullYear(parseInt(year), parseInt(month) - 1, parseInt(day));
                        date.setHours(parseInt(hours), parseInt(minutes), 0, 0);

                        // Конвертируем в Unix timestamp (секунды)
                        return Math.floor(date.getTime() / 1000);

                    } catch (error) {
                        console.error('[GroupOrderInfo] Ошибка создания timestamp:', error);
                        return Math.floor(Date.now() / 1000); // Fallback на текущее время
                    }
                }

                // Вычисляет время до начала для первого сообщения (формат: "18 Hours 31 Minutes")
                calculateTimeUntilStartForFirstMessage(dateStr, timeStr) {
                    try {
                        // Получаем текущее время CET/CEST
                        const cetNow = this.getCurrentCETTime();

                        // Парсим дату и время старта (формат: 20.07.2025 22:00)
                        const [day, month, year] = dateStr.split('.');
                        const [hours, minutes] = timeStr.split(':');

                        // Создаем дату старта (предполагаем, что время уже в CET)
                        const startDate = new Date();
                        startDate.setFullYear(parseInt(year), parseInt(month) - 1, parseInt(day));
                        startDate.setHours(parseInt(hours), parseInt(minutes), 0, 0);

                        // Вычисляем разность в минутах
                        const diffMs = startDate.getTime() - cetNow.getTime();
                        const diffMinutes = Math.round(diffMs / (1000 * 60));

                        if (diffMinutes <= 0) {
                            return 'now (activity has started)';
                        } else if (diffMinutes < 60) {
                            return `${diffMinutes} Minutes`;
                        } else {
                            const hours = Math.floor(diffMinutes / 60);
                            const remainingMinutes = diffMinutes % 60;
                            if (remainingMinutes === 0) {
                                return `${hours} Hours`;
                            } else {
                                return `${hours} Hours ${remainingMinutes} Minutes`;
                            }
                        }

                    } catch (error) {
                        console.error('[GroupOrderInfo] Ошибка вычисления времени для первого сообщения:', error);
                        return 'time calculation error';
                    }
                }

                // Форматирует первое сообщение
                formatFirstMessage(timestamp, timeUntilStart, raidTime) {
                    // Вычисляем интервал инвайтов: время рейда + 10 минут
                    const inviteTimeRange = this.calculateInviteTimeRange(raidTime);

                    return `Hello, I confirmed your **RAID** spot <t:${timestamp}:f>
 **:timer: Raid Start in ${timeUntilStart}**. I will send invite in raid between ${inviteTimeRange} server time (CEST).

If you have any questions about a service you have purchased, you can ask them here!

Also a reminder of the simple rules for your safety:
 * don't write open logs
* don't text us in game chats (write here)
* be sure to hit the boss


||Thank you for taking the time to read this and Thank you for order and choosing OverGear :slight_smile:
*If you want to play again with our team, you can order on the site OverGear.com and ask for @meldteam.euraids ! We will be happy to help you again.||
                                 Have a good time and good loot, see u in game* :wink:
\`\`\`fix
we want to inform you, due to that last boss maybe will be split we will need in some circumstances to kill last boss in 10-25 mins after 7th boss \`\`\``;
                }

                // Вычисляет интервал времени для отправки инвайтов (время рейда + 10 минут)
                calculateInviteTimeRange(raidTime) {
                    try {
                        // Парсим время рейда (формат: HH:MM)
                        const [hours, minutes] = raidTime.split(':');
                        const raidHour = parseInt(hours);
                        const raidMinute = parseInt(minutes);

                        // Вычисляем время окончания интервала (время рейда + 10 минут)
                        let endHour = raidHour;
                        let endMinute = raidMinute + 10;

                        // Обрабатываем переход через час
                        if (endMinute >= 60) {
                            endHour += 1;
                            endMinute -= 60;
                        }

                        // Форматируем время с ведущими нулями
                        const formatTime = (h, m) => `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}`;

                        const startTime = formatTime(raidHour, raidMinute);
                        const endTime = formatTime(endHour, endMinute);

                        return `${startTime} - ${endTime}`;

                    } catch (error) {
                        console.error('[GroupOrderInfo] Ошибка вычисления интервала инвайтов:', error);
                        return '19:15 - 19:25'; // Fallback на фиксированное время
                    }
                }

                // Отправляет сообщение с доступными рейдами
                async sendAvailableRaids(raidType) {
                    console.log(`[GroupOrderInfo] Формируем сообщение с ${raidType} рейдами`);

                    try {
                        this.showNotification(`⚔️ Получаем расписание ${raidType} рейдов...`, 'info');

                        // Получаем отфильтрованные рейды напрямую с сервера
                        const config = this.SHEETS_CONFIG;
                        const baseUrl = config.PROXY_URL.replace('/api/sheets', '');
                        const url = `${baseUrl}/api/schedule/${raidType}`;

                        const response = await fetch(url, {
                            method: 'GET',
                            headers: {
                                'Accept': 'application/json',
                                'X-API-Key': config.API_TOKEN
                            }
                        });

                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }

                        const result = await response.json();

                        if (!result.success) {
                            throw new Error(result.error || 'Неизвестная ошибка сервера');
                        }

                        const availableRaids = result.data;
                        console.log(`[GroupOrderInfo] Получено ${availableRaids.length} ${raidType} рейдов с сервера`);

                        if (availableRaids.length === 0) {
                            this.showNotification(`ℹ️ Нет доступных ${raidType} рейдов на эту неделю`, 'info');
                            return;
                        }

                        // Формируем сообщение
                        const message = this.formatRaidsMessage(availableRaids, raidType);

                        // Вставляем сообщение в поле ввода чата
                        await this.insertMessageIntoChat(message);

                        this.showNotification(`✅ Сообщение с ${raidType} рейдами сформировано`, 'success');

                    } catch (error) {
                        console.error(`[GroupOrderInfo] Ошибка при получении ${raidType} рейдов:`, error);
                        this.showNotification(`❌ Ошибка при получении ${raidType} рейдов: ${error.message}`, 'error');
                    }
                }

                // Обертка для sendAvailableRaids с индикатором загрузки
                async sendAvailableRaidsWithLoading(button, raidType) {
                    try {
                        // Включаем индикатор загрузки
                        this.setButtonLoading(button, true);

                        // Выполняем основную логику
                        await this.sendAvailableRaids(raidType);

                    } finally {
                        // Выключаем индикатор загрузки
                        this.setButtonLoading(button, false);
                    }
                }

                // Управляет состоянием загрузки кнопки
                setButtonLoading(button, isLoading) {
                    if (isLoading) {
                        // Сохраняем оригинальный текст и цвет
                        button.dataset.originalText = button.innerHTML;
                        button.dataset.originalBackground = button.style.background;

                        // Устанавливаем индикатор загрузки
                        button.innerHTML = '⏳ Загрузка...';
                        button.style.background = '#6c757d';
                        button.disabled = true;
                        button.style.cursor = 'not-allowed';

                    } else {
                        // Восстанавливаем оригинальный вид
                        if (button.dataset.originalText) {
                            button.innerHTML = button.dataset.originalText;
                        }
                        if (button.dataset.originalBackground) {
                            button.style.background = button.dataset.originalBackground;
                        }
                        button.disabled = false;
                        button.style.cursor = 'pointer';

                        // Очищаем сохраненные данные
                        delete button.dataset.originalText;
                        delete button.dataset.originalBackground;
                    }
                }

                // Получает данные расписания с сервера (кеширование теперь на сервере)
                async fetchScheduleData() {
                    try {
                        console.log('[GroupOrderInfo] Получаем расписание с сервера...');

                        const config = this.SHEETS_CONFIG;
                        const baseUrl = config.PROXY_URL.replace('/api/sheets', '');
                        const url = `${baseUrl}/api/schedule`;

                        const response = await fetch(url, {
                            method: 'GET',
                            headers: {
                                'Accept': 'application/json',
                                'X-API-Key': config.API_TOKEN
                            }
                        });

                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }

                        const result = await response.json();

                        if (!result.success) {
                            throw new Error(result.error || 'Неизвестная ошибка сервера');
                        }

                        console.log(`[GroupOrderInfo] Получено ${result.total_records} записей расписания с сервера`);
                        console.log(`[GroupOrderInfo] Статистика сервера:`, result.stats);

                        return {
                            result: result.result,
                            message: result.message,
                            data: result.data,
                            total_records: result.total_records
                        };

                    } catch (error) {
                        console.error('[GroupOrderInfo] Ошибка получения расписания с сервера:', error);
                        throw error;
                    }
                }



                // Фильтрует доступные рейды по типу и активности
                filterAvailableRaids(scheduleData, raidType) {
                    try {
                        console.log(`[GroupOrderInfo] Фильтруем ${raidType} рейды из ${scheduleData.length} записей`);

                        const filteredRaids = scheduleData.filter(entry => {
                            // Проверяем, что есть событие
                            if (!entry.event || typeof entry.event !== 'string') {
                                return false;
                            }

                            // Проверяем, что active поле пустое (доступно для записи)
                            if (entry.active && entry.active.trim() !== '') {
                                return false;
                            }

                            const eventLower = entry.event.toLowerCase();

                            // Фильтруем по типу рейда
                            if (raidType === 'normal') {
                                // Ищем точно "LoU Normal"
                                return eventLower === 'lou normal';
                            } else if (raidType === 'heroic') {
                                // Ищем точно "LoU Heroic"
                                return eventLower === 'lou heroic';
                            } else if (raidType === 'mythic') {
                                // Ищем все что содержит "LoU Mythic"
                                return eventLower.includes('lou mythic');
                            }

                            return false;
                        });

                        console.log(`[GroupOrderInfo] Найдено ${filteredRaids.length} доступных ${raidType} рейдов`);
                        return filteredRaids;

                    } catch (error) {
                        console.error('[GroupOrderInfo] Ошибка фильтрации рейдов:', error);
                        return [];
                    }
                }

                // Форматирует сообщение с доступными рейдами
                formatRaidsMessage(availableRaids, raidType) {
                    try {
                        const raidTypeCapitalized = raidType.charAt(0).toUpperCase() + raidType.slice(1);
                        let message = `Available ${raidTypeCapitalized} raids left this week:\n`;

                        // Группируем рейды по дням
                        const raidsByDay = {};

                        availableRaids.forEach(raid => {
                            const dayKey = `${raid.date} ${raid.day}`;
                            if (!raidsByDay[dayKey]) {
                                raidsByDay[dayKey] = [];
                            }
                            raidsByDay[dayKey].push(raid);
                        });

                        // Сортируем дни по дате
                        const sortedDays = Object.keys(raidsByDay).sort((a, b) => {
                            const dateA = a.split(' ')[0];
                            const dateB = b.split(' ')[0];

                            // Парсим даты в формате DD.MM.YYYY
                            const [dayA, monthA, yearA] = dateA.split('.').map(Number);
                            const [dayB, monthB, yearB] = dateB.split('.').map(Number);

                            const fullDateA = new Date(yearA, monthA - 1, dayA);
                            const fullDateB = new Date(yearB, monthB - 1, dayB);

                            return fullDateA - fullDateB;
                        });

                        // Формируем сообщение по дням
                        sortedDays.forEach(dayKey => {
                            const raids = raidsByDay[dayKey];
                            const [date, dayName] = dayKey.split(' ', 2);

                            message += `\n${date} ${dayName}\n`;

                            // Сортируем рейды по времени
                            raids.sort((a, b) => {
                                const timeA = a.time || '';
                                const timeB = b.time || '';
                                return timeA.localeCompare(timeB);
                            });

                            raids.forEach(raid => {
                                message += `${raid.time} ${raid.event}\n`;
                            });
                        });

                        console.log(`[GroupOrderInfo] Сформировано сообщение для ${raidType} рейдов:`, message);
                        return message;

                    } catch (error) {
                        console.error('[GroupOrderInfo] Ошибка форматирования сообщения:', error);
                        return `Available ${raidType} raids left this week:\nОшибка форматирования данных`;
                    }
                }

                // Вычисляет время до начала активности
                calculateTimeUntilStart(dateStr, timeStr) {
                    try {
                        // Получаем текущее время CET/CEST
                        const cetNow = this.getCurrentCETTime();

                        // Парсим дату и время старта (формат: 20.07.2025 22:00)
                        const [day, month, year] = dateStr.split('.');
                        const [hours, minutes] = timeStr.split(':');

                        // Создаем дату старта (предполагаем, что время уже в CET)
                        const startDate = new Date();
                        startDate.setFullYear(parseInt(year), parseInt(month) - 1, parseInt(day));
                        startDate.setHours(parseInt(hours), parseInt(minutes), 0, 0);

                        // Вычисляем разность в минутах
                        const diffMs = startDate.getTime() - cetNow.getTime();
                        const diffMinutes = Math.round(diffMs / (1000 * 60));

                        console.log(`[GroupOrderInfo] Текущее время CET: ${cetNow.toLocaleString('ru-RU')}`);
                        console.log(`[GroupOrderInfo] Время старта: ${startDate.toLocaleString('ru-RU')}`);
                        console.log(`[GroupOrderInfo] Разность в минутах: ${diffMinutes}`);

                        if (diffMinutes <= 0) {
                            return 'now (activity has started)';
                        } else if (diffMinutes < 60) {
                            return `${diffMinutes}-${diffMinutes + 10} minutes`;
                        } else {
                            const hours = Math.floor(diffMinutes / 60);
                            const remainingMinutes = diffMinutes % 60;
                            if (remainingMinutes === 0) {
                                return `${hours} hours`;
                            } else {
                                return `${hours} hours ${remainingMinutes}-${remainingMinutes + 10} minutes`;
                            }
                        }

                    } catch (error) {
                        console.error('[GroupOrderInfo] Ошибка вычисления времени:', error);
                        return 'time calculation error';
                    }
                }

                // Получает текущее время CET/CEST через браузер
                getCurrentCETTime() {
                    return this.getBrowserCETTime();
                }

                // Получает время CET/CEST через браузер
                getBrowserCETTime() {
                    try {
                        // Используем Intl API для получения времени в конкретной временной зоне
                        const now = new Date();

                        // Создаем дату в часовом поясе Europe/Berlin
                        const cetTime = new Date(now.toLocaleString("en-US", {
                            timeZone: "Europe/Berlin"
                        }));

                        console.log(`[GroupOrderInfo] Получено время CET через браузер: ${cetTime.toLocaleString()}`);
                        return cetTime;

                    } catch (error) {
                        console.warn('[GroupOrderInfo] Ошибка браузерного времени, используем ручной расчет');

                        // Последний резерв - ручной расчет
                        const now = new Date();
                        const utc = now.getTime() + (now.getTimezoneOffset() * 60000);

                        // Определяем, летнее время или нет
                        const isDST = this.isDaylightSavingTime(now);
                        const cetOffset = isDST ? 2 : 1; // CEST или CET

                        const cetTime = new Date(utc + (cetOffset * 3600000));
                        console.log(`[GroupOrderInfo] Вычислено время CET вручную: ${cetTime.toLocaleString()}`);
                        return cetTime;
                    }
                }

                // Определяет, действует ли летнее время (CEST)
                isDaylightSavingTime(date) {
                    const year = date.getFullYear();

                    // Летнее время в Европе: последнее воскресенье марта - последнее воскресенье октября
                    const marchLastSunday = this.getLastSundayOfMonth(year, 2); // март = 2
                    const octoberLastSunday = this.getLastSundayOfMonth(year, 9); // октябрь = 9

                    return date >= marchLastSunday && date < octoberLastSunday;
                }

                // Находит последнее воскресенье месяца
                getLastSundayOfMonth(year, month) {
                    const lastDay = new Date(year, month + 1, 0); // последний день месяца
                    const lastSunday = new Date(lastDay);
                    lastSunday.setDate(lastDay.getDate() - lastDay.getDay()); // вычитаем дни до воскресенья
                    return lastSunday;
                }

                // Вставляет сообщение в поле ввода чата Discord
                async insertMessageIntoChat(message) {
                    try {
                        // Ищем поле ввода Discord (Slate.js редактор)
                        const chatInput = document.querySelector('[data-slate-editor="true"]');

                        if (chatInput) {
                            console.log('[GroupOrderInfo] Найдено поле ввода Slate.js');

                            // Используем Clipboard API для безопасной вставки
                            await this.insertViaClipboard(chatInput, message);

                        } else {
                            // Пробуем альтернативные способы
                            console.warn('[GroupOrderInfo] Slate.js поле не найдено, пробуем альтернативы');
                            await this.tryAlternativeInsert(message);
                        }

                    } catch (error) {
                        console.error('[GroupOrderInfo] Ошибка вставки в чат:', error);
                        this.showNotification('❌ Ошибка вставки сообщения в чат', 'error');
                    }
                }

                // Вставка через события (безопасно для Slate.js)
                async insertViaClipboard(element, text) {
                    try {
                        console.log('[GroupOrderInfo] Вставляем через события');

                        // Фокусируемся на элементе
                        element.focus();

                        // Очищаем поле через выделение и удаление
                        const selection = window.getSelection();
                        const range = document.createRange();
                        range.selectNodeContents(element);
                        selection.removeAllRanges();
                        selection.addRange(range);

                        // Создаем событие paste с данными
                        const clipboardData = new DataTransfer();
                        clipboardData.setData('text/plain', text);

                        const pasteEvent = new ClipboardEvent('paste', {
                            bubbles: true,
                            cancelable: true,
                            clipboardData: clipboardData
                        });

                        element.dispatchEvent(pasteEvent);

                        console.log('[GroupOrderInfo] Текст вставлен через события');
                        this.showNotification('✅ Сообщение добавлено в чат', 'success');

                    } catch (error) {
                        console.error('[GroupOrderInfo] Ошибка вставки через события:', error);
                        this.showNotification('❌ Ошибка вставки сообщения в чат', 'error');
                    }
                }

                // Альтернативные способы вставки
                async tryAlternativeInsert(message) {
                    try {
                        console.log('[GroupOrderInfo] Пробуем альтернативную вставку');

                        // Просто копируем в буфер обмена
                        await navigator.clipboard.writeText(message);

                        // Ищем любое поле ввода для фокуса
                        const anyInput = document.querySelector('[contenteditable="true"]') ||
                                       document.querySelector('textarea') ||
                                       document.querySelector('input[type="text"]');

                        if (anyInput) {
                            anyInput.focus();
                            console.log('[GroupOrderInfo] Фокус установлен на поле ввода');
                        }

                        this.showNotification('📋 Текст скопирован в буфер обмена. Нажмите Ctrl+V в поле ввода чата.', 'info');

                    } catch (error) {
                        console.error('[GroupOrderInfo] Ошибка альтернативной вставки:', error);
                        this.showNotification('❌ Ошибка копирования текста', 'error');
                    }
                }

                // Подключение к серверу поиска в Discord с автоматическим переподключением
                connectToDiscordSearchServer() {
                    try {
                        this.debug('Подключаемся к серверу поиска в Discord...');

                        // Закрываем существующее соединение если есть
                        if (this.discordSearchWs) {
                            this.discordSearchWs.close();
                        }

                        this.discordSearchWs = new WebSocket(this.discordSearchWsUrl);

                        this.discordSearchWs.onopen = () => {
                            this.info('Подключено к серверу поиска в Discord');
                            this.reconnectAttempts = 0; // Сбрасываем счетчик попыток

                            // Генерируем или сохраняем pluginId
                            if (!this.pluginId) {
                                this.pluginId = 'GroupOrderInfo_' + Date.now();
                            }

                            // Регистрируемся как Discord плагин с кодом активации
                            this.discordSearchWs.send(JSON.stringify({
                                type: 'DISCORD_PLUGIN_REGISTER',
                                pluginId: this.pluginId,
                                activationCode: this.activationCode
                            }));

                            this.showNotification('🔍 Поиск в Discord подключен', 'success');
                        };

                        this.discordSearchWs.onmessage = (event) => {
                            try {
                                const data = JSON.parse(event.data);
                                console.log('[GroupOrderInfo] Получено сообщение:', data);

                                if (data.type === 'SEARCH_IN_DISCORD' && data.chatName) {
                                    this.searchInDiscord(data.chatName);
                                } else if (data.type === 'ACTIVATION_CODE' && data.code) {
                                    this.handleActivationCode(data.code);
                                }
                            } catch (error) {
                                console.error('[GroupOrderInfo] Ошибка обработки сообщения:', error);
                            }
                        };

                        this.discordSearchWs.onclose = () => {
                            console.log('[GroupOrderInfo] Отключено от сервера поиска в Discord');
                            this.discordSearchWs = null;

                            // Автоматическое переподключение
                            this.scheduleReconnect();
                        };

                        this.discordSearchWs.onerror = (error) => {
                            console.error('[GroupOrderInfo] Ошибка WebSocket поиска:', error);
                            this.discordSearchWs = null;

                            // Автоматическое переподключение при ошибке
                            this.scheduleReconnect();
                        };

                    } catch (error) {
                        console.error('[GroupOrderInfo] Ошибка подключения к серверу поиска:', error);
                        this.showNotification('❌ Ошибка подключения к серверу поиска', 'error');

                        // Автоматическое переподключение при ошибке
                        this.scheduleReconnect();
                    }
                }

                // Планирование переподключения с экспоненциальной задержкой
                scheduleReconnect() {
                    if (this.reconnectInterval) {
                        clearTimeout(this.reconnectInterval);
                    }

                    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
                        console.log('[GroupOrderInfo] Достигнуто максимальное количество попыток переподключения');
                        this.showNotification('❌ Не удалось подключиться к серверу. Перезапустите Discord.', 'error');
                        return;
                    }

                    this.reconnectAttempts++;
                    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts - 1), 30000); // Максимум 30 секунд

                    console.log(`[GroupOrderInfo] Переподключение через ${delay}ms (попытка ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

                    this.reconnectInterval = setTimeout(() => {
                        if (!this.discordSearchWs || this.discordSearchWs.readyState !== WebSocket.OPEN) {
                            this.connectToDiscordSearchServer();
                        }
                    }, delay);
                }

                // Отключение от сервера поиска в Discord
                disconnectFromDiscordSearchServer() {
                    // Очищаем интервал переподключения
                    if (this.reconnectInterval) {
                        clearTimeout(this.reconnectInterval);
                        this.reconnectInterval = null;
                    }

                    if (this.discordSearchWs) {
                        console.log('[GroupOrderInfo] Отключаемся от сервера поиска в Discord');
                        this.discordSearchWs.close();
                        this.discordSearchWs = null;
                    }

                    // Сбрасываем счетчик попыток
                    this.reconnectAttempts = 0;
                }

                // Поиск чата в Discord по названию
                searchInDiscord(chatName) {
                    try {
                        console.log(`[GroupOrderInfo] Ищем чат в Discord: ${chatName}`);

                        const foundChannel = this.findChatByName(chatName);

                        if (foundChannel) {
                            this.log(`Найден чат: ${foundChannel.name || chatName}`);

                            // Переходим к найденному чату
                            const success = this.navigateToChannel(foundChannel.id);

                            if (success) {
                                // Активируем окно Discord
                                setTimeout(() => window.focus(), 500);
                                this.showNotification(`✅ Переход в чат: ${foundChannel.name || chatName}`, 'success');
                            } else {
                                this.showNotification(`❌ Не удалось перейти в чат: ${foundChannel.name || chatName}`, 'error');
                            }
                        } else {
                            console.log(`[GroupOrderInfo] Чат "${chatName}" не найден`);
                            this.showNotification(`❌ Чат "${chatName}" не найден`, 'error');
                        }

                    } catch (error) {
                        console.error('[GroupOrderInfo] Ошибка поиска в Discord:', error);
                        this.showNotification('❌ Ошибка поиска в Discord', 'error');
                    }
                }

                // Логика поиска чата (аналогично DiscordItalicaHelper)
                findChatByName(chatName) {
                    const nameLower = chatName.toLowerCase().trim();
                    let foundChannel = null;

                    // Поиск среди пользователей (ЛС)
                    if (this.UserStore && this.ChannelStore) {
                        const users = this.UserStore.getUsers();
                        for (const userId in users) {
                            const user = users[userId];
                            if (!user) continue;

                            const usernameLower = user.username.toLowerCase();
                            const globalNameLower = user.globalName?.toLowerCase();

                            if (usernameLower === nameLower ||
                                (globalNameLower && globalNameLower === nameLower) ||
                                (user.discriminator && user.discriminator !== '0' && `${usernameLower}#${user.discriminator}` === nameLower)) {

                                const dmChannelId = this.ChannelStore.getDMFromUserId(userId);
                                if (dmChannelId) {
                                    foundChannel = this.ChannelStore.getChannel(dmChannelId);
                                    if (foundChannel) {
                                        this.log(`Найден ЛС с пользователем ${user.username}`);
                                        break;
                                    }
                                }
                            }
                        }
                    }

                    // Поиск среди групповых чатов
                    if (!foundChannel && this.ChannelStore && this.ChannelStore.getSortedPrivateChannels) {
                        const sortedPrivateChannels = this.ChannelStore.getSortedPrivateChannels();
                        const groupChannels = sortedPrivateChannels.filter(ch => ch.type === 3);

                        this.log(`Проверяем ${groupChannels.length} групповых чатов`);

                        for (const channel of groupChannels) {
                            if (!channel || !channel.recipients) continue;

                            // Точное совпадение по имени группы
                            if (channel.name && channel.name.toLowerCase() === nameLower) {
                                foundChannel = channel;
                                this.log(`Найдена группа по имени: ${channel.name}`);
                                break;
                            }

                            // Поиск по участникам группы
                            const recipientNames = channel.recipients
                                .map(userId => this.UserStore?.getUser(userId)?.username)
                                .filter(Boolean)
                                .sort()
                                .join(', ')
                                .toLowerCase();

                            if (recipientNames === nameLower) {
                                foundChannel = channel;
                                this.log(`Найдена группа по участникам: ${recipientNames}`);
                                break;
                            }
                        }

                        // Поиск по частичному совпадению имени группы
                        if (!foundChannel) {
                            for (const channel of groupChannels) {
                                if (channel && channel.name && channel.name.toLowerCase().includes(nameLower)) {
                                    foundChannel = channel;
                                    console.log(`[GroupOrderInfo] Найдена группа по частичному совпадению: ${channel.name}`);
                                    break;
                                }
                            }
                        }
                    }

                    return foundChannel;
                }

                // Инициализация кода активации
                initializeActivationCode() {
                    try {
                        // Получаем или генерируем код активации
                        this.activationCode = this.getStorageItem('GroupOrderInfo_activationCode');

                        if (!this.activationCode) {
                            // Генерируем новый код: 3 буквы + 3 цифры
                            const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
                            const numbers = '0123456789';

                            let code = '';
                            for (let i = 0; i < 3; i++) {
                                code += letters.charAt(Math.floor(Math.random() * letters.length));
                            }
                            for (let i = 0; i < 3; i++) {
                                code += numbers.charAt(Math.floor(Math.random() * numbers.length));
                            }

                            this.activationCode = code;
                            this.setStorageItem('GroupOrderInfo_activationCode', this.activationCode);
                            console.log(`[GroupOrderInfo] Сгенерирован новый код активации: ${this.activationCode}`);
                        } else {
                            console.log(`[GroupOrderInfo] Загружен код активации: ${this.activationCode}`);
                        }

                        // Проверяем статус активации
                        this.isActivated = this.getStorageItem('GroupOrderInfo_isActivated') === 'true';
                        console.log(`[GroupOrderInfo] Статус активации: ${this.isActivated}`);
                    } catch (error) {
                        console.error('[GroupOrderInfo] Ошибка инициализации кода активации:', error);
                        // Fallback - генерируем код без сохранения
                        const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
                        const numbers = '0123456789';
                        let code = '';
                        for (let i = 0; i < 3; i++) {
                            code += letters.charAt(Math.floor(Math.random() * letters.length));
                        }
                        for (let i = 0; i < 3; i++) {
                            code += numbers.charAt(Math.floor(Math.random() * numbers.length));
                        }
                        this.activationCode = code;
                        this.isActivated = false;
                        console.log(`[GroupOrderInfo] Fallback код активации: ${this.activationCode}`);
                    }
                }

                // Безопасное получение из хранилища
                getStorageItem(key) {
                    try {
                        // Пробуем BetterDiscord API (если доступен)
                        if (typeof window !== 'undefined' && window.BdApi && window.BdApi.Data) {
                            return window.BdApi.Data.load('GroupOrderInfo', key);
                        }

                        // Пробуем разные способы доступа к localStorage
                        if (typeof window !== 'undefined' && window.localStorage) {
                            return window.localStorage.getItem(key);
                        }
                        if (typeof localStorage !== 'undefined') {
                            return localStorage.getItem(key);
                        }
                        if (typeof global !== 'undefined' && global.localStorage) {
                            return global.localStorage.getItem(key);
                        }

                        // Если localStorage недоступен, используем временное хранилище
                        if (!this.tempStorage) {
                            this.tempStorage = {};
                        }
                        return this.tempStorage[key] || null;
                    } catch (error) {
                        console.error('[GroupOrderInfo] Ошибка получения из хранилища:', error);
                        // Fallback к временному хранилищу
                        if (!this.tempStorage) {
                            this.tempStorage = {};
                        }
                        return this.tempStorage[key] || null;
                    }
                }

                // Безопасное сохранение в хранилище
                setStorageItem(key, value) {
                    try {
                        // Пробуем BetterDiscord API (если доступен)
                        if (typeof window !== 'undefined' && window.BdApi && window.BdApi.Data) {
                            window.BdApi.Data.save('GroupOrderInfo', key, value);
                            return;
                        }

                        // Пробуем разные способы доступа к localStorage
                        if (typeof window !== 'undefined' && window.localStorage) {
                            window.localStorage.setItem(key, value);
                            return;
                        }
                        if (typeof localStorage !== 'undefined') {
                            localStorage.setItem(key, value);
                            return;
                        }
                        if (typeof global !== 'undefined' && global.localStorage) {
                            global.localStorage.setItem(key, value);
                            return;
                        }

                        // Если localStorage недоступен, используем временное хранилище
                        if (!this.tempStorage) {
                            this.tempStorage = {};
                        }
                        this.tempStorage[key] = value;
                    } catch (error) {
                        console.error('[GroupOrderInfo] Ошибка сохранения в хранилище:', error);
                        // Fallback к временному хранилищу
                        if (!this.tempStorage) {
                            this.tempStorage = {};
                        }
                        this.tempStorage[key] = value;
                    }
                }

                // Генерирует новый код активации
                generateActivationCode() {
                    const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
                    const numbers = '0123456789';
                    let code = '';

                    // 3 буквы
                    for (let i = 0; i < 3; i++) {
                        code += letters.charAt(Math.floor(Math.random() * letters.length));
                    }
                    // 3 цифры
                    for (let i = 0; i < 3; i++) {
                        code += numbers.charAt(Math.floor(Math.random() * numbers.length));
                    }

                    return code;
                }

                // Сбрасывает активацию плагина
                resetActivation() {
                    console.log('[GroupOrderInfo] Сброс активации плагина');

                    // Показываем диалог подтверждения
                    const confirmDialog = document.createElement('div');
                    confirmDialog.style.cssText = `
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0, 0, 0, 0.8);
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        z-index: 10000;
                    `;

                    const dialogContent = document.createElement('div');
                    dialogContent.style.cssText = `
                        background: var(--background-primary);
                        border-radius: 8px;
                        padding: 20px;
                        max-width: 400px;
                        text-align: center;
                        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
                    `;

                    dialogContent.innerHTML = `
                        <div style="color: var(--text-normal); font-size: 16px; margin-bottom: 15px;">
                            🔄 Сброс активации
                        </div>
                        <div style="color: var(--text-muted); font-size: 14px; margin-bottom: 20px; line-height: 1.4;">
                            Вы уверены, что хотите сбросить активацию плагина?<br>
                            После сброса потребуется ввести новый код активации в расширении.
                        </div>
                        <div style="display: flex; gap: 10px; justify-content: center;">
                            <button id="confirmReset" style="
                                background: var(--button-danger-background);
                                color: white;
                                border: none;
                                border-radius: 4px;
                                padding: 8px 16px;
                                cursor: pointer;
                                font-size: 14px;
                            ">Сбросить</button>
                            <button id="cancelReset" style="
                                background: var(--button-secondary-background);
                                color: var(--text-normal);
                                border: none;
                                border-radius: 4px;
                                padding: 8px 16px;
                                cursor: pointer;
                                font-size: 14px;
                            ">Отмена</button>
                        </div>
                    `;

                    confirmDialog.appendChild(dialogContent);
                    document.body.appendChild(confirmDialog);

                    // Обработчики кнопок
                    const confirmBtn = confirmDialog.querySelector('#confirmReset');
                    const cancelBtn = confirmDialog.querySelector('#cancelReset');

                    confirmBtn.addEventListener('click', () => {
                        // Выполняем сброс
                        this.performReset();
                        confirmDialog.remove();
                    });

                    cancelBtn.addEventListener('click', () => {
                        confirmDialog.remove();
                    });

                    // Закрытие по клику вне диалога
                    confirmDialog.addEventListener('click', (e) => {
                        if (e.target === confirmDialog) {
                            confirmDialog.remove();
                        }
                    });
                }

                // Выполняет сброс активации
                performReset() {
                    console.log('[GroupOrderInfo] Выполняем сброс активации');

                    // Генерируем новый код активации
                    this.activationCode = this.generateActivationCode();
                    console.log(`[GroupOrderInfo] Новый код активации: ${this.activationCode}`);

                    // Сохраняем новый код в localStorage
                    this.setStorageItem('GroupOrderInfo_activationCode', this.activationCode);

                    // Сбрасываем статус активации
                    this.isActivated = false;
                    this.setStorageItem('GroupOrderInfo_isActivated', 'false');

                    // Отключаем WebSocket соединение
                    this.disconnectFromDiscordSearchServer();

                    // Сбрасываем ID плагина
                    this.pluginId = null;

                    // Удаляем информацию о заказах и показываем код активации
                    this.removeOrderInfo();
                    this.showActivationCode();

                    // Показываем уведомление
                    this.showNotification('🔄 Активация сброшена. Введите новый код в расширении: ' + this.activationCode, 'info', 5000);

                    // Переподключаемся к серверу с новым кодом
                    setTimeout(() => {
                        this.connectToDiscordSearchServer();
                    }, 1000);
                }

                // Показывает код активации вместо информации о заказе (только в групповых чатах)
                async showActivationCode() {
                    // Проверяем, что мы в групповом чате
                    if (!this.SelectedChannelStore) {
                        this.log('SelectedChannelStore не инициализирован', 'error');
                        return;
                    }

                    const channelId = this.SelectedChannelStore.getChannelId();
                    if (!channelId) {
                        this.log('Нет выбранного канала, не показываем код активации');
                        return;
                    }

                    if (!this.ChannelStore) {
                        this.log('ChannelStore не инициализирован', 'error');
                        return;
                    }

                    const channel = this.ChannelStore.getChannel(channelId);
                    if (!channel || channel.type !== 3) { // 3 = GROUP_DM
                        this.log('Не групповой чат, не показываем код активации');
                        return;
                    }

                    // Если контейнер уже существует, просто меняем его содержимое
                    if (this.orderInfoContainer) {
                        console.log('[GroupOrderInfo] Обновляем существующий контейнер кодом активации');
                        this.updateContainerWithActivationCode();
                        return;
                    }

                    // Создаем новый контейнер только если его нет
                    console.log('[GroupOrderInfo] Создаем новый контейнер для кода активации');
                    const activationElement = await this.createOrderInfoElement([]);
                    this.updateElementWithActivationCode(activationElement);

                    // Размещаем элемент
                    this.placeOrderInfoContainer(activationElement);
                }

                // Обновляет существующий контейнер кодом активации
                updateContainerWithActivationCode() {
                    if (!this.orderInfoContainer) return;

                    this.orderInfoContainer.innerHTML = '';
                    this.updateElementWithActivationCode(this.orderInfoContainer);
                }

                // Обновляет элемент содержимым кода активации
                updateElementWithActivationCode(element) {
                    // Создаем заголовок в том же стиле, что и для заказов
                    const title = document.createElement('div');
                    title.style.cssText = 'font-weight: 600; margin-bottom: 8px; color: var(--brand-experiment); font-size: 13px; display: flex; justify-content: space-between; align-items: center;';
                    title.innerHTML = `<span>🔗 Активация плагина</span>`;

                    // Создаем контент активации
                    const content = document.createElement('div');
                    content.innerHTML = `
                        <div style="color: var(--text-normal); font-size: 11px; margin-bottom: 8px; line-height: 1.3;">
                            Введите код в расширение в таблице:
                        </div>
                        <div style="display: flex; align-items: center; gap: 4px; margin-bottom: 8px;">
                            <div style="
                                background: var(--background-secondary);
                                border: 1px solid var(--background-modifier-accent);
                                border-radius: 4px;
                                padding: 6px 8px;
                                font-family: 'Consolas', monospace;
                                font-size: 14px;
                                font-weight: bold;
                                color: var(--brand-experiment);
                                letter-spacing: 1px;
                                flex: 1;
                                text-align: center;
                            ">${this.activationCode}</div>
                            <button id="copyCodeBtn" style="
                                background: var(--brand-experiment);
                                color: white;
                                border: none;
                                border-radius: 4px;
                                padding: 6px 8px;
                                cursor: pointer;
                                font-size: 10px;
                                transition: background 0.2s;
                            ">Копировать</button>
                        </div>
                        <div style="display: flex; gap: 4px;">
                            <button id="refreshBtn" style="
                                background: var(--button-secondary-background);
                                color: var(--text-normal);
                                border: none;
                                border-radius: 4px;
                                padding: 6px 8px;
                                cursor: pointer;
                                font-size: 10px;
                                flex: 1;
                                transition: background 0.2s;
                            ">🔄 Обновить</button>
                        </div>
                    `;

                    element.appendChild(title);
                    element.appendChild(content);

                    this.setupActivationButtons(element);
                }

                // Настраивает обработчики кнопок активации
                setupActivationButtons(element) {
                    // Обработчик копирования
                    const copyBtn = element.querySelector('#copyCodeBtn');
                    copyBtn.addEventListener('click', () => {
                        navigator.clipboard.writeText(this.activationCode).then(() => {
                            copyBtn.textContent = 'Скопировано!';
                            copyBtn.style.background = '#43b581';
                            setTimeout(() => {
                                copyBtn.textContent = 'Копировать';
                                copyBtn.style.background = 'var(--brand-experiment)';
                            }, 2000);
                        });
                    });

                    copyBtn.addEventListener('mouseenter', () => {
                        if (copyBtn.textContent === 'Копировать') {
                            copyBtn.style.background = '#4752c4';
                        }
                    });

                    copyBtn.addEventListener('mouseleave', () => {
                        if (copyBtn.textContent === 'Копировать') {
                            copyBtn.style.background = 'var(--brand-experiment)';
                        }
                    });

                    // Обработчик кнопки "Обновить"
                    const refreshBtn = element.querySelector('#refreshBtn');
                    refreshBtn.addEventListener('click', () => {
                        console.log('[GroupOrderInfo] Проверяем статус активации...');

                        // Обновляем статус активации из хранилища
                        this.isActivated = this.getStorageItem('GroupOrderInfo_isActivated') === 'true';

                        if (this.isActivated) {
                            console.log('[GroupOrderInfo] Плагин активирован! Обновляем интерфейс');
                            this.showNotification('✅ Плагин активирован!', 'success');
                            setTimeout(() => this.updateOrderInfo(), 500);
                        } else {
                            console.log('[GroupOrderInfo] Плагин все еще не активирован');
                            refreshBtn.textContent = '❌ Не активирован';
                            refreshBtn.style.background = '#f04747';
                            refreshBtn.style.color = 'white';
                            setTimeout(() => {
                                refreshBtn.textContent = '🔄 Обновить';
                                refreshBtn.style.background = 'var(--button-secondary-background)';
                                refreshBtn.style.color = 'var(--text-normal)';
                            }, 2000);
                        }
                    });

                    refreshBtn.addEventListener('mouseenter', () => {
                        if (refreshBtn.textContent === '🔄 Обновить') {
                            refreshBtn.style.background = 'var(--button-secondary-background-hover)';
                        }
                    });

                    refreshBtn.addEventListener('mouseleave', () => {
                        if (refreshBtn.textContent === '🔄 Обновить') {
                            refreshBtn.style.background = 'var(--button-secondary-background)';
                        }
                    });
                }

                // Размещает контейнер с информацией о заказах/активации (используем ту же логику, что и для заказов)
                placeOrderInfoContainer(element) {
                    // Находим место для вставки (под списком пользователей) - используем те же селекторы, что и в оригинальном коде
                    let sidebar = document.querySelector('[class*="members_"]');
                    if (!sidebar) {
                        sidebar = document.querySelector('[class*="members-"]');
                    }
                    if (!sidebar) {
                        sidebar = document.querySelector('div[class*="members"]');
                    }

                    console.log('[GroupOrderInfo] Найден sidebar для размещения:', !!sidebar);
                    console.log('[GroupOrderInfo] Sidebar element:', sidebar);

                    if (sidebar) {
                        this.orderInfoContainer = element;
                        sidebar.appendChild(this.orderInfoContainer);
                        console.log('[GroupOrderInfo] Элемент добавлен в sidebar');
                    } else {
                        // Альтернативный способ - ищем правую панель
                        const rightPanel = document.querySelector('aside');
                        if (rightPanel) {
                            this.orderInfoContainer = element;
                            rightPanel.appendChild(this.orderInfoContainer);
                            console.log('[GroupOrderInfo] Элемент добавлен в aside');
                        } else {
                            // Последний вариант - ищем любой контейнер справа
                            const container = document.querySelector('[class*="sidebar"]');
                            if (container) {
                                this.orderInfoContainer = element;
                                container.appendChild(this.orderInfoContainer);
                                console.log('[GroupOrderInfo] Элемент добавлен в container');
                            } else {
                                console.log('[GroupOrderInfo] Не удалось найти место для вставки');
                            }
                        }
                    }
                }

                // Обработка кода активации
                handleActivationCode(receivedCode) {
                    console.log(`[GroupOrderInfo] Получен код активации: ${receivedCode}`);

                    if (receivedCode === this.activationCode) {
                        console.log('[GroupOrderInfo] Код активации верный, активируем плагин');

                        this.isActivated = true;
                        this.setStorageItem('GroupOrderInfo_isActivated', 'true');

                        this.showNotification('✅ Плагин успешно активирован!', 'success');

                        // Обновляем интерфейс
                        setTimeout(() => {
                            this.updateOrderInfo();
                        }, 1000);

                        // Отправляем подтверждение активации
                        if (this.discordSearchWs && this.discordSearchWs.readyState === WebSocket.OPEN) {
                            this.discordSearchWs.send(JSON.stringify({
                                type: 'ACTIVATION_SUCCESS',
                                code: receivedCode,
                                message: 'Плагин успешно активирован'
                            }));
                        }
                    } else {
                        console.log('[GroupOrderInfo] Неверный код активации');
                        this.showNotification('❌ Неверный код активации', 'error');

                        // Отправляем ошибку активации
                        if (this.discordSearchWs && this.discordSearchWs.readyState === WebSocket.OPEN) {
                            this.discordSearchWs.send(JSON.stringify({
                                type: 'ACTIVATION_ERROR',
                                code: receivedCode,
                                message: 'Неверный код активации'
                            }));
                        }
                    }
                }

                // Удаляет информацию о заказе
                removeOrderInfo() {
                    if (this.orderInfoContainer) {
                        this.orderInfoContainer.remove();
                        this.orderInfoContainer = null;
                    }
                }

                // Настройка связи с Chrome Extension
                setupExtensionCommunication() {
                    // Слушаем сообщения от Chrome Extension
                    window.addEventListener('message', (event) => {
                        if (event.source !== window) return;

                        console.log('[GroupOrderInfo] Получено сообщение:', event.data);

                        if (event.data.type === 'ORDER_FINDER_READY') {
                            this.extensionReady = true;
                            console.log('[GroupOrderInfo] Chrome Extension готов');
                        }

                        if (event.data.type === 'ORDER_FINDER_RESULT') {
                            this.handleSearchResult(event.data);
                        }

                        // Команды для управления debug режимом
                        if (event.data.type === 'DEBUG_COMMAND') {
                            if (event.data.command === 'enable_debug') {
                                this.debugMode = true;
                                this.setStorageItem('GroupOrderInfo_debugMode', 'true');
                                this.info('Debug режим включен');
                            } else if (event.data.command === 'disable_debug') {
                                this.debugMode = false;
                                this.setStorageItem('GroupOrderInfo_debugMode', 'false');
                                this.info('Debug режим выключен');
                            }
                        }
                    });
                }

                // Обработка результата поиска
                handleSearchResult(result) {
                    console.log('[GroupOrderInfo] Результат поиска:', result);

                    // Показываем уведомление пользователю
                    if (result.success) {
                        this.showNotification('✅ ' + result.message, 'success');
                    } else {
                        this.showNotification('❌ ' + result.message, 'error');
                    }
                }

    // Показ уведомления
    showNotification(message, type = 'info') {
        // Используем BdApi для показа уведомлений
        this.showToast(message, { type: type });
    }

    // Запускает оптимизированный слушатель изменений канала
    startChannelListener() {
        this.log('Запуск слушателя каналов');

        // Более частая проверка для быстрой реакции
        this.channelCheckInterval = setInterval(() => {
            if (!this.SelectedChannelStore) return;

            const newChannelId = this.SelectedChannelStore.getChannelId();
            if (newChannelId && newChannelId !== this.currentChannelId) {
                this.info('Смена канала: ' + this.currentChannelId + ' -> ' + newChannelId);

                // Отменяем предыдущие операции
                if (this.updateTimeout) {
                    clearTimeout(this.updateTimeout);
                    this.updateTimeout = null;
                }

                if (this.currentRequest) {
                    this.currentRequest.abort();
                    this.currentRequest = null;
                }

                this.processingChannel = null;
                this.currentChannelId = newChannelId;

                // Быстрое обновление с минимальной задержкой
                setTimeout(() => this.updateOrderInfo(), 100);
            }
        }, 500); // Проверяем каждые 500ms вместо 2000ms
    }

    // Навигация к каналу с несколькими способами
    navigateToChannel(channelId) {
        this.log(`Попытка перехода к каналу: ${channelId}`);

        try {
            // Способ 1: Используем NavigationUtils.transitionTo
            if (this.NavigationUtils && typeof this.NavigationUtils.transitionTo === 'function') {
                this.log('Используем NavigationUtils.transitionTo');
                this.log('NavigationUtils объект: ' + JSON.stringify(Object.keys(this.NavigationUtils)));
                this.NavigationUtils.transitionTo(`/channels/@me/${channelId}`);
                this.log('transitionTo вызван успешно');
                return true;
            } else {
                this.log('NavigationUtils недоступен или не имеет метода transitionTo');
                if (this.NavigationUtils) {
                    this.log('Доступные методы NavigationUtils: ' + Object.keys(this.NavigationUtils).join(', '));
                } else {
                    this.log('NavigationUtils равен null/undefined');
                }
            }

            // Способ 2: Пробуем найти другой модуль навигации
            const RouterUtils = BdApi.Webpack.getModule(m => m.transitionTo && typeof m.transitionTo === 'function');
            if (RouterUtils && RouterUtils.transitionTo) {
                this.log('Используем RouterUtils.transitionTo');
                RouterUtils.transitionTo(`/channels/@me/${channelId}`);
                return true;
            }

            // Способ 3: Пробуем найти модуль через другие фильтры
            const NavigationModule = BdApi.Webpack.getModule(BdApi.Webpack.Filters.byStrings('transitionTo'));
            if (NavigationModule && NavigationModule.transitionTo) {
                this.log('Используем NavigationModule.transitionTo');
                NavigationModule.transitionTo(`/channels/@me/${channelId}`);
                return true;
            }

            // Способ 3.5: Пробуем найти через Router
            const Router = BdApi.Webpack.getModule(BdApi.Webpack.Filters.byProps('push', 'replace'));
            if (Router && Router.push) {
                this.log('Используем Router.push');
                Router.push(`/channels/@me/${channelId}`);
                return true;
            }

            // Способ 4: Используем history API напрямую
            if (window.history && window.history.pushState) {
                this.log('Используем window.history.pushState');
                const url = `/channels/@me/${channelId}`;
                window.history.pushState(null, '', url);

                // Диспатчим событие для обновления интерфейса
                window.dispatchEvent(new PopStateEvent('popstate'));
                return true;
            }

            // Способ 5: Изменяем location напрямую
            if (window.location) {
                this.log('Используем window.location');
                window.location.hash = `/channels/@me/${channelId}`;
                return true;
            }

            this.log('Все способы навигации не сработали', 'error');
            return false;

        } catch (error) {
            this.log('Ошибка при навигации: ' + error.message, 'error');
            return false;
        }
    }
}

// --- ВАЖНО: НЕ УДАЛЯЙТЕ ЭТУ СТРОКУ ---
// Проверка на отсутствие ZeresPluginLibrary для совместимости
if (!global.ZeresPluginLibrary) {
    GroupOrderInfo.prototype.load = () => BdApi.alert("Отсутствует ZeresPluginLibrary", "Для работы некоторых функций может требоваться библиотека ZeresPluginLibrary. Основная функциональность доступна без неё.");
}
// --- КОНЕЦ ВАЖНОЙ СТРОКИ ---

// Глобальные функции для управления debug режимом
window.GroupOrderInfo_enableDebug = function() {
    window.postMessage({ type: 'DEBUG_COMMAND', command: 'enable_debug' }, '*');
    console.log('[GroupOrderInfo] Debug режим включен. Перезагрузите страницу для полного эффекта.');
};

window.GroupOrderInfo_disableDebug = function() {
    window.postMessage({ type: 'DEBUG_COMMAND', command: 'disable_debug' }, '*');
    console.log('[GroupOrderInfo] Debug режим выключен. Перезагрузите страницу для полного эффекта.');
};

// Экспортируем класс для загрузчика
module.exports = GroupOrderInfo;
