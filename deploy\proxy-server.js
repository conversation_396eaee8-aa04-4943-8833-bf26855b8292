require('dotenv').config();
const express = require('express');
const cors = require('cors');
const { google } = require('googleapis');
const WebSocket = require('ws');
const http = require('http');
const ScheduleService = require('./schedule-service');

const app = express();
const port = process.env.PORT || 3000;

// Настройка CORS для работы с Google Docs и Discord
const corsOptions = {
    origin: [
        'https://docs.google.com',
        'https://discord.com',
        'https://ptb.discord.com',
        'chrome-extension://*',
        'http://localhost:3000',
        'https://discord-6r4h.onrender.com'
    ],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-API-Key',
        'Cache-Control',
        'Accept',
        'Origin',
        'X-Requested-With'
    ],
    credentials: true,
    optionsSuccessStatus: 200
};

app.use(cors(corsOptions));
app.use(express.json());

// API токены из переменных окружения
const API_TOKENS = {
    PLUGIN_TOKEN: process.env.PLUGIN_API_TOKEN || 'meldteamfjlsjpqpo1298d3f892',
    EXTENSION_TOKEN: process.env.EXTENSION_API_TOKEN || 'extmeldtokenri3029uf'
};

console.log('[Proxy] API токены загружены:', {
    PLUGIN_TOKEN: API_TOKENS.PLUGIN_TOKEN ? '***' + API_TOKENS.PLUGIN_TOKEN.slice(-4) : 'НЕ УСТАНОВЛЕН',
    EXTENSION_TOKEN: API_TOKENS.EXTENSION_TOKEN ? '***' + API_TOKENS.EXTENSION_TOKEN.slice(-4) : 'НЕ УСТАНОВЛЕН'
});

// Middleware для проверки API токенов
function validateApiToken(req, res, next) {
    // Пропускаем WebSocket соединения и тестовый эндпоинт
    if (req.path === '/api/test' || req.method === 'OPTIONS') {
        return next();
    }

    const token = req.headers['x-api-key'] || req.headers['authorization']?.replace('Bearer ', '');

    if (!token) {
        console.log(`[Proxy] Отклонен запрос без токена: ${req.method} ${req.path}`);
        return res.status(401).json({
            success: false,
            error: 'API токен обязателен. Добавьте заголовок X-API-Key или Authorization.'
        });
    }

    // Проверяем токен
    const validTokens = Object.values(API_TOKENS);
    if (!validTokens.includes(token)) {
        console.log(`[Proxy] Отклонен запрос с неверным токеном: ${req.method} ${req.path}, токен: ***${token.slice(-4)}`);
        return res.status(403).json({
            success: false,
            error: 'Неверный API токен.'
        });
    }

    console.log(`[Proxy] Токен валиден для запроса: ${req.method} ${req.path}`);
    next();
}

// Применяем проверку токенов ко всем API эндпоинтам
app.use('/api', validateApiToken);

// Service Account данные из переменных окружения
let SERVICE_ACCOUNT;

try {
    if (!process.env.GOOGLE_SERVICE_ACCOUNT_KEY) {
        console.error('[Proxy] Ошибка: Отсутствует переменная окружения GOOGLE_SERVICE_ACCOUNT_KEY');
        console.error('[Proxy] Пожалуйста, настройте переменную окружения согласно .env.example');
        process.exit(1);
    }

    // Парсим JSON из переменной окружения
    SERVICE_ACCOUNT = JSON.parse(process.env.GOOGLE_SERVICE_ACCOUNT_KEY);

    // Проверяем наличие обязательных полей в JSON
    const requiredFields = ['project_id', 'private_key', 'client_email'];
    const missingFields = requiredFields.filter(field => !SERVICE_ACCOUNT[field]);

    if (missingFields.length > 0) {
        console.error('[Proxy] Ошибка: В GOOGLE_SERVICE_ACCOUNT_KEY отсутствуют обязательные поля:', missingFields.join(', '));
        process.exit(1);
    }

    console.log('[Proxy] Service Account успешно загружен для проекта:', SERVICE_ACCOUNT.project_id);

} catch (error) {
    console.error('[Proxy] Ошибка парсинга GOOGLE_SERVICE_ACCOUNT_KEY:', error.message);
    console.error('[Proxy] Убедитесь, что переменная содержит валидный JSON');
    process.exit(1);
}

// Инициализация Google Sheets API
const auth = new google.auth.GoogleAuth({
    credentials: SERVICE_ACCOUNT,
    scopes: [
        'https://www.googleapis.com/auth/spreadsheets'  // Полные права на чтение и запись
    ]
});

const sheets = google.sheets({ version: 'v4', auth });

// Система синхронизации данных с адаптивным обновлением
class SmartTableSyncer {
    constructor() {
        this.cachedData = null;
        this.isRunning = false;
        this.fastInterval = 15000; // 15 секунд при наличии Discord плагинов
        this.slowInterval = 600000; // 10 минут без Discord плагинов
        this.currentInterval = this.slowInterval; // По умолчанию медленный режим
        this.intervalId = null;
        this.spreadsheetId = '1gL6WSAa0XaaOKm7hmyK7LCFG3JOul3umqhqi_hd3ElI';
        this.sheetName = 'Oper';
    }

    async syncFullTable() {
        try {
            console.log('[Syncer] Загружаем полную таблицу...');

            // Загружаем полную таблицу
            const response = await sheets.spreadsheets.values.get({
                spreadsheetId: this.spreadsheetId,
                range: `${this.sheetName}!A1:AR2000`
            });

            this.cachedData = response.data.values || [];
            console.log(`[Syncer] Загружено строк: ${this.cachedData.length}`);

        } catch (error) {
            console.error('[Syncer] Ошибка загрузки таблицы:', error.message);
            throw error;
        }
    }

    // Проверка наличия активных Discord плагинов
    hasActiveDiscordPlugins() {
        // Проверяем, есть ли подключенные Discord плагины
        for (const pluginData of connectedDiscordPlugins.values()) {
            if (pluginData.ws && pluginData.ws.readyState === 1) { // WebSocket.OPEN = 1
                return true;
            }
        }
        return false;
    }

    // Обновление интервала синхронизации в зависимости от наличия Discord плагинов
    updateSyncInterval() {
        const hasPlugins = this.hasActiveDiscordPlugins();
        const newInterval = hasPlugins ? this.fastInterval : this.slowInterval;

        if (newInterval !== this.currentInterval) {
            this.currentInterval = newInterval;
            const mode = hasPlugins ? 'быстрый (15 сек)' : 'медленный (10 мин)';
            console.log(`[Syncer] Переключение на ${mode} режим синхронизации`);

            // Перезапускаем таймер с новым интервалом
            if (this.isRunning) {
                this.restartTimer();
            }
        }
    }

    // Перезапуск таймера с текущим интервалом
    restartTimer() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }

        this.intervalId = setInterval(() => {
            this.syncFullTable().catch(error => {
                console.error('[Syncer] Ошибка периодического обновления:', error.message);
            });
        }, this.currentInterval);
    }

    // Поиск данных в кеше
    findOrderData(orderNumber) {
        if (!this.cachedData) {
            return [];
        }

        const results = [];
        for (let i = 0; i < this.cachedData.length; i++) {
            const row = this.cachedData[i];
            if (row[4] === orderNumber) { // Столбец E (индекс 4)
                results.push({
                    rowNumber: i + 1,
                    b: row[1] || '',     // Date (столбец B)
                    composition: row[20] || '', // Composition (столбец U)
                    o: row[14] || '',    // Link (столбец O)
                    q: row[16] || '',    // Nickname (столбец Q)
                    t: row[19] || '',    // Time (столбец T)
                    v: row[21] || '',    // Task (столбец V)
                    ar: row[43] || ''    // Checkbox (столбец AR)
                });
            }
        }

        return results;
    }

    start() {
        if (this.isRunning) {
            console.log('[Syncer] Синхронизация уже запущена');
            return;
        }

        // Определяем начальный режим синхронизации
        this.updateSyncInterval();
        const mode = this.currentInterval === this.fastInterval ? 'быстрый (15 сек)' : 'медленный (30 мин)';
        console.log(`[Syncer] Запускаем адаптивную синхронизацию в ${mode} режиме...`);
        this.isRunning = true;

        // Первоначальная загрузка
        this.syncFullTable().then(() => {
            console.log('[Syncer] Первоначальная загрузка завершена');
        }).catch(error => {
            console.error('[Syncer] Ошибка первоначальной загрузки:', error.message);
        });

        // Запускаем таймер с текущим интервалом
        this.restartTimer();
    }

    stop() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
        this.isRunning = false;
        console.log('[Syncer] Синхронизация остановлена');
    }

    getStats() {
        const hasPlugins = this.hasActiveDiscordPlugins();
        const mode = this.currentInterval === this.fastInterval ? 'fast_15_seconds' : 'slow_30_minutes';

        return {
            isRunning: this.isRunning,
            cachedRowsCount: this.cachedData ? this.cachedData.length : 0,
            currentInterval: this.currentInterval,
            fastInterval: this.fastInterval,
            slowInterval: this.slowInterval,
            syncMode: 'adaptive',
            currentMode: mode,
            hasActiveDiscordPlugins: hasPlugins,
            activeDiscordPluginsCount: connectedDiscordPlugins.size
        };
    }

    // Принудительное обновление кеша (для использования после изменений)
    async forceRefresh() {
        console.log('[Syncer] Принудительное обновление кеша...');
        try {
            await this.syncFullTable();
            console.log('[Syncer] Кеш принудительно обновлен');
        } catch (error) {
            console.error('[Syncer] Ошибка при принудительном обновлении кеша:', error);
        }
    }
}

// Создаем экземпляр синхронизатора
const tableSyncer = new SmartTableSyncer();

// Создаем экземпляр сервиса расписания с Service Account
const scheduleService = new ScheduleService(SERVICE_ACCOUNT);

// Эндпоинт для получения данных из Google Sheets (теперь использует кеш)
app.get('/api/sheets/:spreadsheetId/:sheetName', async (req, res) => {
    try {
        const { orderNumber, forceUpdate } = req.query;

        console.log(`[Proxy] Запрос данных для заказа: ${orderNumber}${forceUpdate ? ' (принудительно)' : ''}`);

        // Если запрошено принудительное обновление, игнорируем кеш
        if (forceUpdate === 'true') {
            console.log('[Proxy] Принудительное обновление - игнорируем кеш');
            const { spreadsheetId, sheetName } = req.params;
            const range = `${sheetName}!A1:AR2000`;
            const response = await sheets.spreadsheets.values.get({
                spreadsheetId,
                range
            });

            const values = response.data.values || [];
            const results = [];
            for (let i = 0; i < values.length; i++) {
                const row = values[i];
                if (row[4] === orderNumber) {
                    results.push({
                        rowNumber: i + 1,
                        b: row[1] || '',     // Date (столбец B)
                        o: row[14] || '',    // Link (столбец O)
                         composition: row[20] || '', // Composition (столбец U)
                        q: row[16] || '',    // Nickname (столбец Q)
                        t: row[19] || '',    // Time (столбец T)
                        v: row[21] || '',    // Task (столбец V)
                        ar: row[43] || ''    // Checkbox (столбец AR)
                    });
                }
            }

            console.log(`[Proxy] Принудительное обновление: найдено записей: ${results.length}`);
            return res.json({
                success: true,
                data: results,
                source: 'force_update',
                timestamp: new Date().toISOString()
            });
        }

        // Проверяем, что синхронизатор запущен и имеет данные
        if (!tableSyncer.isRunning || !tableSyncer.cachedData) {
            console.log('[Proxy] Кеш не готов, выполняем прямой запрос к Google Sheets...');

            // Fallback к прямому запросу если кеш не готов
            const { spreadsheetId, sheetName } = req.params;
            const range = `${sheetName}!A1:AR2000`;
            const response = await sheets.spreadsheets.values.get({
                spreadsheetId,
                range
            });

            const values = response.data.values || [];
            const results = [];
            for (let i = 0; i < values.length; i++) {
                const row = values[i];
                if (row[4] === orderNumber) {
                    results.push({
                        rowNumber: i + 1,
                        b: row[1] || '',     // Date (столбец B)
                        o: row[14] || '',    // Link (столбец O)
                         composition: row[20] || '', // Composition (столбец U)
                        q: row[16] || '',    // Nickname (столбец Q)
                        t: row[19] || '',    // Time (столбец T)
                        v: row[21] || '',    // Task (столбец V)
                        ar: row[43] || ''    // Checkbox (столбец AR)
                    });
                }
            }

            console.log(`[Proxy] Прямой запрос: найдено записей: ${results.length}`);
            return res.json({ success: true, data: results, source: 'direct' });
        }

        // Используем кешированные данные
        const results = tableSyncer.findOrderData(orderNumber);
        console.log(`[Proxy] Из кеша: найдено записей: ${results.length}`);

        res.json({
            success: true,
            data: results,
            source: 'cache',
            cacheStats: tableSyncer.getStats()
        });

    } catch (error) {
        console.error('[Proxy] Ошибка:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Эндпоинт для поиска заказа в таблице (только для активированных пар)
app.post('/api/find-order', (req, res) => {
    const { orderNumber, rowNumber, pluginId } = req.body;
    console.log(`[Proxy] Запрос поиска заказа: ${orderNumber}, строка: ${rowNumber}, от плагина: ${pluginId}`);

    try {
        // Проверяем, есть ли активированная связь для этого плагина
        let hasActiveLink = false;

        if (pluginId) {
            // Ищем связь по кодам активации
            codeConnections.forEach((connection, code) => {
                if (connection.pluginWs) {
                    // Проверяем, что это наш плагин
                    const pluginData = Array.from(connectedDiscordPlugins.values())
                        .find(plugin => plugin.id === pluginId && plugin.ws === connection.pluginWs);

                    if (pluginData) {
                        hasActiveLink = true;
                    }
                }
            });
        }

        if (!hasActiveLink && pluginId) {
            return res.status(403).json({
                success: false,
                message: 'Discord плагин не активирован. Введите код активации в расширении.',
                orderNumber: orderNumber,
                extensionsNotified: 0,
                requiresActivation: true
            });
        }

        // Отправляем команду поиска только в активированные расширения
        const sentCount = sendFindOrderCommand(orderNumber, rowNumber, pluginId);

        if (sentCount > 0) {
            res.json({
                success: true,
                message: `Переход к строке ${rowNumber} выполнен в ${sentCount} активированном расширении(ях)`,
                orderNumber: orderNumber,
                rowNumber: rowNumber,
                extensionsNotified: sentCount
            });
        } else {
            res.json({
                success: false,
                message: 'Нет активированных Chrome расширений. Убедитесь, что расширение активировано кодом.',
                orderNumber: orderNumber,
                extensionsNotified: 0,
                requiresActivation: true
            });
        }

    } catch (error) {
        console.error('[Proxy] Ошибка поиска:', error);
        res.status(500).json({
            success: false,
            message: 'Ошибка при отправке команды поиска: ' + error.message
        });
    }
});

// Эндпоинт для поиска в Discord (только для активированных пар)
app.post('/api/search-discord', (req, res) => {
    const { chatName, extensionId } = req.body;
    console.log(`[Proxy] Запрос поиска в Discord: ${chatName}, от расширения: ${extensionId}`);

    try {
        if (!chatName) {
            return res.status(400).json({
                success: false,
                message: 'Не указано название чата для поиска'
            });
        }

        // Проверяем, есть ли хотя бы одна активная связь
        let hasActiveLink = codeConnections.size > 0;

        console.log(`[Proxy] Проверяем активные связи. Всего связей: ${codeConnections.size}`);

        // Дополнительно проверяем, что связи действительно активны
        if (hasActiveLink) {
            let activeConnectionsCount = 0;
            codeConnections.forEach((connection, code) => {
                if (connection.extensionWs?.readyState === 1 && connection.pluginWs?.readyState === 1) {
                    activeConnectionsCount++;
                    console.log(`[Proxy] Активная связь найдена по коду: ${code}`);
                }
            });
            hasActiveLink = activeConnectionsCount > 0;
        }

        if (!hasActiveLink) {
            console.log(`[Proxy] Нет активных связей`);
            return res.status(403).json({
                success: false,
                message: 'Нет активных связей. Введите код активации из Discord плагина.',
                chatName: chatName,
                pluginsNotified: 0,
                requiresActivation: true
            });
        }

        // Отправляем команду поиска только в связанные Discord плагины
        const sentCount = sendDiscordSearchCommand(chatName, extensionId);

        if (sentCount > 0) {
            res.json({
                success: true,
                message: `Команда поиска "${chatName}" отправлена в ${sentCount} активированный Discord плагин(ов)`,
                chatName: chatName,
                pluginsNotified: sentCount
            });
        } else {
            res.json({
                success: false,
                message: 'Нет активированных Discord плагинов. Убедитесь, что плагин активирован кодом.',
                chatName: chatName,
                pluginsNotified: 0,
                requiresActivation: true
            });
        }

    } catch (error) {
        console.error('[Proxy] Ошибка поиска в Discord:', error);
        res.status(500).json({
            success: false,
            message: 'Ошибка при отправке команды поиска: ' + error.message
        });
    }
});

// Эндпоинт для получения метаданных полей (validation rules)
app.get('/api/field-metadata/:spreadsheetId/:sheetName', async (req, res) => {
    try {
        const { spreadsheetId, sheetName } = req.params;

        console.log(`[Proxy] Запрос метаданных полей для ${spreadsheetId}/${sheetName}`);

        const fieldMetadata = {};

        // Получаем значения для времени из диапазона '1'!$F$2:$F$39
        try {
            const timeResponse = await sheets.spreadsheets.values.get({
                spreadsheetId,
                range: "'1'!F2:F39"
            });

            const timeValues = timeResponse.data.values || [];
            const timeOptions = timeValues
                .map(row => row[0])
                .filter(value => value && value.trim())
                .map(value => value.trim());

            fieldMetadata.time = {
                type: 'select',
                options: timeOptions
            };

            console.log(`[Proxy] Загружено ${timeOptions.length} вариантов времени`);
        } catch (error) {
            console.error('[Proxy] Ошибка загрузки времени:', error);
            fieldMetadata.time = { type: 'text', options: [] };
        }

        // Получаем значения для задач из диапазона '1'!$J$2:$J$49
        try {
            const taskResponse = await sheets.spreadsheets.values.get({
                spreadsheetId,
                range: "'1'!J2:J49"
            });

            const taskValues = taskResponse.data.values || [];
            const taskOptions = taskValues
                .map(row => row[0])
                .filter(value => value && value.trim())
                .map(value => value.trim());

            fieldMetadata.task = {
                type: 'select',
                options: taskOptions
            };

            console.log(`[Proxy] Загружено ${taskOptions.length} вариантов задач`);
        } catch (error) {
            console.error('[Proxy] Ошибка загрузки задач:', error);
            fieldMetadata.task = { type: 'text', options: [] };
        }

        // Получаем значения для состава из диапазона '1'!$L$2:$L$18
        try {
            const compositionResponse = await sheets.spreadsheets.values.get({
                spreadsheetId,
                range: "'1'!L2:L18"
            });

            const compositionValues = compositionResponse.data.values || [];
            const compositionOptions = compositionValues
                .map(row => row[0])
                .filter(value => value && value.trim())
                .map(value => value.trim());

            fieldMetadata.composition = {
                type: 'select',
                options: compositionOptions
            };

            console.log(`[Proxy] Загружено ${compositionOptions.length} вариантов состава`);
        } catch (error) {
            console.error('[Proxy] Ошибка загрузки состава:', error);
            fieldMetadata.composition = { type: 'text', options: [] };
        }

        // Добавляем метаданные для остальных полей
        fieldMetadata.date = {
            type: 'date',
            options: []
        };

        fieldMetadata.nickname = {
            type: 'text',
            options: []
        };

        fieldMetadata.link = {
            type: 'url',
            options: []
        };

        console.log('[Proxy] Найденные метаданные полей:', fieldMetadata);

        res.json({
            success: true,
            metadata: fieldMetadata
        });

    } catch (error) {
        console.error('[Proxy] Ошибка при получении метаданных полей:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Эндпоинт для обновления строки в Google Sheets
app.post('/api/update-row', async (req, res) => {
    try {
        const { spreadsheetId, sheetName, rowNumber, changes } = req.body;

        console.log(`[Proxy] Обновление строки ${rowNumber} в таблице ${spreadsheetId}/${sheetName}`);
        console.log('[Proxy] Изменения:', changes);

        if (!spreadsheetId || !sheetName || !rowNumber || !changes) {
            return res.status(400).json({
                success: false,
                error: 'Отсутствуют обязательные параметры'
            });
        }

        // Маппинг полей на колонки Google Sheets
        const fieldToColumn = {
            'date': 'B',      // Дата
            'composition': 'U', // Состав
            'time': 'T',      // Время
            'task': 'V',      // Задача
            'nickname': 'Q',  // Никнейм
            'link': 'O'       // Ссылка
        };

        // Подготавливаем обновления для каждой колонки
        const updatesRaw = [];
        const updatesUserEntered = [];
        let timeUpdate = null;

        for (const [field, value] of Object.entries(changes)) {
            const column = fieldToColumn[field];
            if (column) {
                const range = `${sheetName}!${column}${rowNumber}`;

                if (field === 'time') {
                    // Сохраняем обновление времени для отдельной обработки
                    timeUpdate = { range, value, column, rowNumber };
                } else if (field === 'date') {
                    // Дату обрабатываем как USER_ENTERED
                    updatesUserEntered.push({
                        range: range,
                        values: [[value]]
                    });
                } else {
                    // Остальные поля как RAW
                    updatesRaw.push({
                        range: range,
                        values: [[value]]
                    });
                }
                console.log(`[Proxy] Обновление ${range} = "${value}"`);
            }
        }

        if (updatesRaw.length === 0 && updatesUserEntered.length === 0 && !timeUpdate) {
            return res.status(400).json({
                success: false,
                error: 'Нет валидных полей для обновления'
            });
        }

        let totalUpdatedCells = 0;

        // Выполняем batch update для обычных полей (RAW)
        if (updatesRaw.length > 0) {
            const batchUpdateResponse = await sheets.spreadsheets.values.batchUpdate({
                spreadsheetId,
                requestBody: {
                    valueInputOption: 'RAW',
                    data: updatesRaw
                }
            });
            totalUpdatedCells += batchUpdateResponse.data.totalUpdatedCells;
        }

        // Выполняем batch update для даты (USER_ENTERED)
        if (updatesUserEntered.length > 0) {
            const batchUpdateResponseUserEntered = await sheets.spreadsheets.values.batchUpdate({
                spreadsheetId,
                requestBody: {
                    valueInputOption: 'USER_ENTERED',
                    data: updatesUserEntered
                }
            });
            totalUpdatedCells += batchUpdateResponseUserEntered.data.totalUpdatedCells;
        }

        // Обрабатываем время отдельно с форматированием как TEXT
        if (timeUpdate) {
            // Получаем информацию о листах для определения правильного sheetId
            const spreadsheetInfo = await sheets.spreadsheets.get({
                spreadsheetId,
                fields: 'sheets.properties'
            });

            // Ищем лист по имени или берем первый
            let targetSheetId = 0;
            if (spreadsheetInfo.data.sheets) {
                const targetSheet = spreadsheetInfo.data.sheets.find(sheet =>
                    sheet.properties.title === sheetName
                );
                if (targetSheet) {
                    targetSheetId = targetSheet.properties.sheetId;
                } else {
                    // Если не нашли по имени, берем первый лист
                    targetSheetId = spreadsheetInfo.data.sheets[0].properties.sheetId;
                }
            }

            await sheets.spreadsheets.batchUpdate({
                spreadsheetId,
                requestBody: {
                    requests: [
                        {
                            updateCells: {
                                range: {
                                    sheetId: targetSheetId,
                                    startRowIndex: timeUpdate.rowNumber - 1,
                                    endRowIndex: timeUpdate.rowNumber,
                                    startColumnIndex: timeUpdate.column.charCodeAt(0) - 65, // T = 19
                                    endColumnIndex: timeUpdate.column.charCodeAt(0) - 64
                                },
                                rows: [
                                    {
                                        values: [
                                            {
                                                userEnteredValue: {
                                                    stringValue: timeUpdate.value
                                                },
                                                userEnteredFormat: {
                                                    numberFormat: {
                                                        type: 'TEXT'
                                                    }
                                                }
                                            }
                                        ]
                                    }
                                ],
                                fields: 'userEnteredValue,userEnteredFormat.numberFormat'
                            }
                        }
                    ]
                }
            });
            totalUpdatedCells += 1;
        }

        console.log(`[Proxy] Обновлено ${totalUpdatedCells} ячеек`);

        // Принудительно обновляем кеш синхронизатора
        if (tableSyncer.isRunning) {
            console.log('[Proxy] Принудительное обновление кеша после изменений');
            await tableSyncer.forceRefresh();
        }

        res.json({
            success: true,
            updatedCells: totalUpdatedCells,
            message: 'Изменения успешно сохранены'
        });

    } catch (error) {
        console.error('[Proxy] Ошибка при обновлении строки:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Эндпоинт для получения расписания рейдов
app.get('/api/schedule', async (req, res) => {
    try {
        const { forceRefresh } = req.query;
        console.log(`[Proxy] Запрос расписания${forceRefresh === 'true' ? ' (принудительно)' : ''}`);

        const scheduleData = await scheduleService.getSchedule(forceRefresh === 'true');

        res.json({
            success: true,
            ...scheduleData,
            stats: scheduleService.getStats()
        });

    } catch (error) {
        console.error('[Proxy] Ошибка получения расписания:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Эндпоинт для получения отфильтрованных рейдов по типу
app.get('/api/schedule/:raidType', async (req, res) => {
    try {
        const { raidType } = req.params;
        const { forceRefresh } = req.query;

        console.log(`[Proxy] Запрос ${raidType} рейдов${forceRefresh === 'true' ? ' (принудительно)' : ''}`);

        // Проверяем валидность типа рейда
        const validTypes = ['normal', 'heroic', 'mythic'];
        if (!validTypes.includes(raidType)) {
            return res.status(400).json({
                success: false,
                error: `Неверный тип рейда. Доступные типы: ${validTypes.join(', ')}`
            });
        }

        const scheduleData = await scheduleService.getSchedule(forceRefresh === 'true');
        const filteredRaids = scheduleService.filterAvailableRaids(scheduleData.data, raidType);

        res.json({
            success: true,
            result: "success",
            message: `Найдено ${filteredRaids.length} доступных ${raidType} рейдов`,
            data: filteredRaids,
            total_records: filteredRaids.length,
            raid_type: raidType,
            stats: scheduleService.getStats()
        });

    } catch (error) {
        console.error(`[Proxy] Ошибка получения ${req.params.raidType} рейдов:`, error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Эндпоинт для очистки кеша расписания (для отладки)
app.post('/api/schedule/clear-cache', (req, res) => {
    try {
        scheduleService.clearCache();
        res.json({
            success: true,
            message: 'Кеш расписания очищен'
        });
    } catch (error) {
        console.error('[Proxy] Ошибка очистки кеша расписания:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Тестовый эндпоинт
app.get('/api/test', (req, res) => {
    const activeCodeConnections = [];
    codeConnections.forEach((connection, code) => {
        activeCodeConnections.push({
            code: code,
            hasExtension: !!connection.extensionWs,
            hasPlugin: !!connection.pluginWs,
            extensionConnected: connection.extensionWs?.readyState === 1,
            pluginConnected: connection.pluginWs?.readyState === 1,
            timestamp: new Date(connection.timestamp).toISOString()
        });
    });

    const oldLinks = [];
    activationLinks.forEach((link, code) => {
        oldLinks.push({
            code: code,
            extensionId: link.extensionId,
            pluginId: link.pluginId,
            timestamp: new Date(link.timestamp).toISOString()
        });
    });

    res.json({
        success: true,
        message: 'Прокси сервер работает!',
        timestamp: new Date().toISOString(),
        connectedExtensions: connectedExtensions.size,
        connectedDiscordPlugins: connectedDiscordPlugins.size,
        activeCodeConnections: activeCodeConnections.length,
        codeConnections: activeCodeConnections,
        oldLinks: oldLinks.length,
        oldLinksData: oldLinks,
        environment: process.env.NODE_ENV || 'development',
        // Добавляем статистику синхронизации
        tableSyncer: tableSyncer.getStats(),
        // Информация о безопасности
        security: {
            apiTokensConfigured: {
                plugin: !!API_TOKENS.PLUGIN_TOKEN,
                extension: !!API_TOKENS.EXTENSION_TOKEN
            },
            corsEnabled: true,
            allowedOrigins: corsOptions.origin
        }
    });
});

// Создаем HTTP сервер
const server = http.createServer(app);

// Создаем WebSocket сервер
const wss = new WebSocket.Server({ server });

// Хранилище подключенных расширений
const connectedExtensions = new Map();

// Хранилище подключенных Discord плагинов
const connectedDiscordPlugins = new Map();

// Хранилище активных связей по коду активации
const activationLinks = new Map(); // код -> { extensionId, pluginId, timestamp }

// Хранилище связей по кодам активации (основное)
const codeConnections = new Map(); // код -> { extensionWs, pluginWs, timestamp }

// Обработка WebSocket подключений
wss.on('connection', (ws) => {
    console.log('[Proxy] Новое WebSocket подключение');

    ws.on('message', (data) => {
        try {
            const message = JSON.parse(data);
            console.log('[Proxy] Получено WebSocket сообщение:', message);

            if (message.type === 'EXTENSION_REGISTER') {
                // Регистрируем расширение
                const extensionId = message.extensionId || `ext_${Date.now()}`;
                const activationCode = message.activationCode;

                // Удаляем старые подключения с тем же extensionId
                connectedExtensions.forEach((extData, oldId) => {
                    if (oldId === extensionId && extData.ws !== ws) {
                        console.log(`[Proxy] Удаляем старое подключение расширения: ${oldId}`);
                        connectedExtensions.delete(oldId);
                        if (extData.ws.readyState === 1) {
                            extData.ws.close();
                        }
                    }
                });

                connectedExtensions.set(extensionId, {
                    ws,
                    id: extensionId,
                    activationCode: activationCode
                });

                console.log(`[Proxy] Расширение зарегистрировано: ${extensionId}, код: ${activationCode}`);

                // Если есть код активации, пытаемся создать связь
                if (activationCode) {
                    const pluginData = Array.from(connectedDiscordPlugins.values())
                        .find(plugin => plugin.activationCode === activationCode);

                    if (pluginData) {
                        // Обновляем существующую связь или создаем новую
                        codeConnections.set(activationCode, {
                            extensionWs: ws,
                            pluginWs: pluginData.ws,
                            timestamp: Date.now()
                        });
                        console.log(`[Proxy] Автоматически создана/обновлена связь по коду: ${activationCode}`);

                        // Уведомляем расширение об успешной активации
                        ws.send(JSON.stringify({
                            type: 'ACTIVATION_SUCCESS',
                            message: 'Автоматически подключено к Discord плагину'
                        }));
                    }
                }

                ws.send(JSON.stringify({
                    type: 'REGISTRATION_SUCCESS',
                    extensionId: extensionId,
                    message: 'Расширение успешно подключено'
                }));
            }

            if (message.type === 'SEARCH_RESULT') {
                // Получили результат поиска от расширения
                console.log(`[Proxy] Результат поиска: ${message.success ? 'успех' : 'ошибка'} - ${message.message}`);
                // Здесь можно сохранить результат или отправить куда-то еще
            }

            if (message.type === 'DISCORD_PLUGIN_REGISTER') {
                // Регистрируем Discord плагин
                const pluginId = message.pluginId || `discord_plugin_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                const activationCode = message.activationCode;

                connectedDiscordPlugins.set(pluginId, {
                    ws,
                    id: pluginId,
                    activationCode: activationCode
                });

                console.log(`[Proxy] Зарегистрирован Discord плагин: ${pluginId}, код: ${activationCode}`);

                // Обновляем интервал синхронизации (переключаемся на быстрый режим)
                tableSyncer.updateSyncInterval();

                // Если есть код активации, пытаемся создать связь
                if (activationCode) {
                    const extensionData = Array.from(connectedExtensions.values())
                        .find(ext => ext.activationCode === activationCode);

                    if (extensionData) {
                        codeConnections.set(activationCode, {
                            extensionWs: extensionData.ws,
                            pluginWs: ws,
                            timestamp: Date.now()
                        });
                        console.log(`[Proxy] Автоматически создана связь по коду: ${activationCode}`);

                        // Уведомляем расширение об успешной активации
                        if (extensionData.ws.readyState === WebSocket.OPEN) {
                            extensionData.ws.send(JSON.stringify({
                                type: 'ACTIVATION_SUCCESS',
                                message: 'Автоматически подключено к Discord плагину',
                                code: activationCode
                            }));
                        }

                        // Уведомляем плагин об успешной активации
                        if (ws.readyState === WebSocket.OPEN) {
                            ws.send(JSON.stringify({
                                type: 'ACTIVATION_SUCCESS',
                                message: 'Расширение автоматически подключено',
                                code: activationCode
                            }));
                            console.log(`[Proxy] Плагин ${pluginId} уведомлен об автоматической активации`);
                        }
                    }
                }

                ws.send(JSON.stringify({
                    type: 'DISCORD_REGISTRATION_SUCCESS',
                    pluginId: pluginId
                }));
            } else if (message.type === 'ACTIVATION_CODE') {
                // Обрабатываем код активации от расширения
                console.log(`[Proxy] Получен код активации: ${message.code} от WebSocket`);

                // Ищем Discord плагин с таким кодом активации
                let targetPlugin = null;
                connectedDiscordPlugins.forEach((pluginData, pluginId) => {
                    if (pluginData.activationCode === message.code) {
                        targetPlugin = { id: pluginId, data: pluginData };
                    }
                });

                if (targetPlugin) {
                    // Создаем связь по коду активации
                    codeConnections.set(message.code, {
                        extensionWs: ws,
                        pluginWs: targetPlugin.data.ws,
                        timestamp: Date.now()
                    });

                    console.log(`[Proxy] Создана связь по коду: ${message.code}`);

                    // Уведомляем расширение об успешной активации
                    ws.send(JSON.stringify({
                        type: 'ACTIVATION_SUCCESS',
                        message: 'Успешно подключено к Discord плагину',
                        code: message.code
                    }));

                    // ВАЖНО: Уведомляем плагин об активации
                    if (targetPlugin.data.ws.readyState === WebSocket.OPEN) {
                        targetPlugin.data.ws.send(JSON.stringify({
                            type: 'ACTIVATION_SUCCESS',
                            message: 'Расширение успешно подключено',
                            code: message.code
                        }));
                        console.log(`[Proxy] Плагин ${targetPlugin.id} уведомлен об активации`);
                    } else {
                        console.log(`[Proxy] Плагин ${targetPlugin.id} недоступен для уведомления`);
                    }
                } else {
                    console.log(`[Proxy] Плагин с кодом ${message.code} не найден`);
                    ws.send(JSON.stringify({
                        type: 'ACTIVATION_ERROR',
                        message: 'Неверный код активации или плагин не подключен'
                    }));
                }
            } else if (message.type === 'ACTIVATION_SUCCESS' || message.type === 'ACTIVATION_ERROR') {
                // Пересылаем результат активации только в связанное расширение
                console.log(`[Proxy] Результат активации от Discord плагина: ${message.type}`);

                // Находим плагин, который отправил результат
                let senderPluginId = null;
                connectedDiscordPlugins.forEach((pluginData, pluginId) => {
                    if (pluginData.ws === ws) {
                        senderPluginId = pluginId;
                    }
                });

                if (senderPluginId) {
                    // Ищем связь с этим плагином
                    let targetExtensionId = null;
                    activationLinks.forEach((link, code) => {
                        if (link.pluginId === senderPluginId) {
                            targetExtensionId = link.extensionId;
                        }
                    });

                    if (targetExtensionId) {
                        const extensionData = connectedExtensions.get(targetExtensionId);
                        if (extensionData && extensionData.ws.readyState === WebSocket.OPEN) {
                            extensionData.ws.send(JSON.stringify(message));
                            console.log(`[Proxy] Результат активации отправлен в связанное расширение: ${targetExtensionId}`);
                        }
                    } else {
                        console.log('[Proxy] Не найдена связь для результата активации');
                    }
                } else {
                    console.log('[Proxy] Не удалось найти ID отправителя плагина');
                }
            }

        } catch (error) {
            console.error('[Proxy] Ошибка обработки WebSocket сообщения:', error);
        }
    });

    ws.on('close', () => {
        console.log('[Proxy] WebSocket подключение закрыто');

        // Удаляем расширение из списка подключенных и связей
        for (const [extensionId, extData] of connectedExtensions.entries()) {
            if (extData.ws === ws) {
                connectedExtensions.delete(extensionId);
                console.log(`[Proxy] Расширение отключено: ${extensionId}`);

                // Удаляем связи по кодам активации
                codeConnections.forEach((connection, code) => {
                    if (connection.extensionWs === ws) {
                        codeConnections.delete(code);
                        console.log(`[Proxy] Удалена связь с кодом: ${code}`);
                    }
                });

                // Удаляем старые связи
                activationLinks.forEach((link, code) => {
                    if (link.extensionId === extensionId) {
                        activationLinks.delete(code);
                    }
                });
                break;
            }
        }

        // Удаляем Discord плагин из списка подключенных и связей
        for (const [pluginId, pluginData] of connectedDiscordPlugins.entries()) {
            if (pluginData.ws === ws) {
                connectedDiscordPlugins.delete(pluginId);
                console.log(`[Proxy] Discord плагин отключен: ${pluginId}`);

                // Обновляем интервал синхронизации (возможно переключение на медленный режим)
                tableSyncer.updateSyncInterval();

                // Удаляем связи по кодам активации
                codeConnections.forEach((connection, code) => {
                    if (connection.pluginWs === ws) {
                        codeConnections.delete(code);
                        console.log(`[Proxy] Удалена связь с кодом: ${code}`);
                    }
                });

                // Удаляем старые связи
                activationLinks.forEach((link, code) => {
                    if (link.pluginId === pluginId) {
                        activationLinks.delete(code);
                    }
                });
                break;
            }
        }
    });
});

// Функция отправки команды поиска только в активированные расширения
function sendFindOrderCommand(orderNumber, rowNumber, senderPluginId = null) {
    const command = {
        type: 'FIND_ORDER_COMMAND',
        orderNumber: orderNumber,
        rowNumber: rowNumber,
        timestamp: Date.now()
    };

    let sentCount = 0;

    // Ищем связи по кодам активации
    codeConnections.forEach((connection, code) => {
        if (connection.pluginWs && connection.extensionWs) {
            // Проверяем, что команда пришла от связанного плагина
            let shouldSend = false;

            if (senderPluginId) {
                // Ищем плагин с указанным ID
                const pluginData = Array.from(connectedDiscordPlugins.values())
                    .find(plugin => plugin.id === senderPluginId);

                if (pluginData && pluginData.ws === connection.pluginWs) {
                    shouldSend = true;
                }
            } else {
                // Если не указан отправитель, отправляем во все активные связи
                shouldSend = true;
            }

            if (shouldSend && connection.extensionWs.readyState === WebSocket.OPEN) {
                connection.extensionWs.send(JSON.stringify(command));
                sentCount++;
                console.log(`[Proxy] Команда поиска отправлена в связанное расширение (код: ${code}, строка: ${rowNumber})`);
            }
        }
    });

    // Если нет активированных связей, отправляем во все расширения (для обратной совместимости)
    if (sentCount === 0) {
        console.log('[Proxy] Нет активированных связей, отправляем во все расширения');
        connectedExtensions.forEach((extData, extensionId) => {
            if (extData.ws.readyState === WebSocket.OPEN) {
                extData.ws.send(JSON.stringify(command));
                sentCount++;
                console.log(`[Proxy] Команда поиска отправлена в расширение: ${extensionId} (строка: ${rowNumber})`);
            }
        });
    }

    return sentCount;
}

// Функция отправки команды поиска только в активированные Discord плагины
function sendDiscordSearchCommand(chatName, senderExtensionId = null) {
    const command = {
        type: 'SEARCH_IN_DISCORD',
        chatName: chatName,
        timestamp: Date.now()
    };

    let sentCount = 0;

    // Ищем связи по кодам активации
    codeConnections.forEach((connection, code) => {
        if (connection.pluginWs && connection.extensionWs) {
            // Проверяем, что команда пришла от связанного расширения
            let shouldSend = false;

            if (senderExtensionId) {
                // Ищем расширение с указанным ID
                const extensionData = Array.from(connectedExtensions.values())
                    .find(ext => ext.id === senderExtensionId);

                if (extensionData && extensionData.ws === connection.extensionWs) {
                    shouldSend = true;
                }
            } else {
                // Если не указан отправитель, отправляем во все активные связи
                shouldSend = true;
            }

            if (shouldSend && connection.pluginWs.readyState === WebSocket.OPEN) {
                connection.pluginWs.send(JSON.stringify(command));
                sentCount++;
                console.log(`[Proxy] Команда поиска в Discord отправлена в связанный плагин (код: ${code})`);
            }
        }
    });

    // Если нет активированных связей, отправляем во все плагины (для обратной совместимости)
    if (sentCount === 0) {
        console.log('[Proxy] Нет активированных связей, отправляем во все плагины');
        connectedDiscordPlugins.forEach((pluginData, pluginId) => {
            if (pluginData.ws.readyState === WebSocket.OPEN) {
                pluginData.ws.send(JSON.stringify(command));
                sentCount++;
                console.log(`[Proxy] Команда поиска в Discord отправлена в плагин: ${pluginId}`);
            }
        });
    }

    return sentCount;
}

server.listen(port, () => {
    console.log(`[Proxy] HTTP сервер запущен на порту ${port}`);
    console.log(`[Proxy] WebSocket сервер запущен на порту ${port}`);
    console.log(`[Proxy] Тестовый URL: http://localhost:${port}/api/test`);
    console.log(`[Proxy] API поиска: http://localhost:${port}/api/find-order`);
    console.log(`[Proxy] Окружение: ${process.env.NODE_ENV || 'development'}`);

    // Запускаем адаптивную синхронизацию таблицы
    console.log(`[Proxy] Запускаем адаптивную синхронизацию таблицы (15 сек с Discord плагинами, 10 мин без них)...`);
    tableSyncer.start();

    // Запускаем сервис расписания
    console.log(`[Proxy] Запускаем сервис расписания с автообновлением каждые 10 минут...`);
    scheduleService.start();
});
